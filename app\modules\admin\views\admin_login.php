<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول الإدارة - Smart Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 300;
        }
        
        .login-header .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            text-align: right;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 1rem;
        }
        
        .footer-text {
            text-align: center;
            margin-top: 1rem;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h2>لوحة الإدارة</h2>
                <p class="mb-0">تسجيل الدخول للوصول إلى النظام</p>
            </div>
            
            <div class="login-body">
                <div id="alert-container"></div>
                
                <form id="admin-login-form" action="<?=cn("auth/ajax_sign_in")?>" method="POST">
                    <div class="form-group">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <div class="input-group">
                            <input type="email" class="form-control with-icon" id="email" name="email" 
                                   value="<EMAIL>" placeholder="أدخل البريد الإلكتروني" required>
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" class="form-control with-icon" id="password" name="password" 
                                   value="123456" placeholder="أدخل كلمة المرور" required>
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember" class="form-check-input">
                        <label for="remember" class="form-check-label">تذكرني</label>
                    </div>
                    
                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="footer-text">
                    <small>Smart Panel - نظام إدارة التسويق الرقمي</small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#admin-login-form').on('submit', function(e) {
                e.preventDefault();
                
                var formData = $(this).serialize();
                var submitBtn = $(this).find('button[type="submit"]');
                
                // تعطيل الزر أثناء الإرسال
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...');
                
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            showAlert('تم تسجيل الدخول بنجاح!', 'success');
                            setTimeout(function() {
                                window.location.href = '<?=admin_url("users")?>';
                            }, 1000);
                        } else {
                            showAlert(response.message || 'خطأ في تسجيل الدخول', 'danger');
                        }
                    },
                    error: function() {
                        showAlert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'danger');
                    },
                    complete: function() {
                        // إعادة تفعيل الزر
                        submitBtn.prop('disabled', false).html('<i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول');
                    }
                });
            });
            
            function showAlert(message, type) {
                var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                               message +
                               '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                               '</div>';
                
                $('#alert-container').html(alertHtml);
                
                // إخفاء التنبيه بعد 5 ثوان
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }
        });
    </script>
</body>
</html>
