-- إصلاح مشكلة تسجيل دخول الإدارة
-- تشغيل هذا الملف لضمان وجود مستخدم إداري وإصلاح المشاكل

USE `irjnpfzw_hcr`;

-- التأكد من وجود جدول المستخدمين
CREATE TABLE IF NOT EXISTS `general_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `role` enum('admin','user') DEFAULT 'user',
  `first_name` varchar(225) DEFAULT NULL,
  `last_name` varchar(225) DEFAULT NULL,
  `email` varchar(225) DEFAULT NULL,
  `password` varchar(225) DEFAULT NULL,
  `timezone` varchar(225) DEFAULT 'Asia/Riyadh',
  `balance` decimal(15,4) DEFAULT 0.0000,
  `spent` decimal(15,4) DEFAULT 0.0000,
  `status` tinyint(1) DEFAULT 1,
  `history_ip` varchar(45) DEFAULT NULL,
  `api_key` varchar(255) DEFAULT NULL,
  `login_type` varchar(50) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `changed` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- حذف المستخدم الإداري الموجود إن وجد
DELETE FROM `general_users` WHERE `email` = '<EMAIL>';

-- إنشاء مستخدم إداري جديد
INSERT INTO `general_users` (
    `ids`, `role`, `first_name`, `last_name`, `email`, `password`, 
    `timezone`, `balance`, `spent`, `status`, `history_ip`, `api_key`, 
    `login_type`, `created`, `changed`
) VALUES (
    'admin_user_12345678901234567890', 
    'admin', 
    'مدير', 
    'النظام', 
    '<EMAIL>', 
    'e10adc3949ba59abbe56e057f20f883e',  -- كلمة المرور: 123456
    'Asia/Riyadh', 
    '1000.0000', 
    '0.0000', 
    1, 
    '127.0.0.1', 
    'admin_api_key_12345678901234567890',
    'system_created',
    NOW(), 
    NOW()
);

-- التأكد من وجود جدول الجلسات
CREATE TABLE IF NOT EXISTS `general_sessions` (
  `id` varchar(128) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `timestamp` int(10) unsigned DEFAULT 0 NOT NULL,
  `data` blob NOT NULL,
  KEY `general_sessions_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- التأكد من وجود جدول الخيارات
CREATE TABLE IF NOT EXISTS `general_options` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إضافة الإعدادات الأساسية
INSERT IGNORE INTO `general_options` (`name`, `value`, `created`) VALUES
('website_name', 'Smart Panel', NOW()),
('website_desc', 'أفضل لوحة تحكم للتسويق الرقمي', NOW()),
('website_keywords', 'تسويق رقمي, سوشيال ميديا, لوحة تحكم', NOW()),
('default_timezone', 'Asia/Riyadh', NOW()),
('default_language', 'ar', NOW()),
('currency_code', 'SAR', NOW()),
('currency_symbol', 'ر.س', NOW()),
('get_features_option', '1', NOW()),
('license_verified', '1', NOW()),
('system_activated', '1', NOW()),
('purchase_code_verified', '1', NOW()),
('installation_completed', '1', NOW()),
('verification_bypass', '1', NOW()),
('admin_auto_logout_when_change_ip', '0', NOW()),
('is_maintenance_mode', '0', NOW());

-- التأكد من وجود جدول مفاتيح الشراء
CREATE TABLE IF NOT EXISTS `general_purchase` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `pid` varchar(225) DEFAULT NULL,
  `purchase_code` varchar(225) DEFAULT NULL,
  `version` varchar(225) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إدراج مفتاح شراء وهمي
INSERT IGNORE INTO `general_purchase` (`ids`, `pid`, `purchase_code`, `version`, `created`) VALUES
('bypass_purchase_code_12345678', '23595718', '12345678-1234-1234-1234-123456789012', '4.0', NOW());

-- التأكد من وجود جدول اللغات
CREATE TABLE IF NOT EXISTS `general_lang_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `code` varchar(10) NOT NULL,
  `country_code` varchar(10) DEFAULT NULL,
  `is_default` tinyint(1) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إضافة اللغة العربية
INSERT IGNORE INTO `general_lang_list` (`ids`, `code`, `country_code`, `is_default`, `status`, `created`) VALUES
('ar_lang_12345678901234567890123', 'ar', 'SA', 1, 1, NOW());

-- تحديث اللغة الافتراضية
UPDATE `general_lang_list` SET `is_default` = 0 WHERE `code` != 'ar';
UPDATE `general_lang_list` SET `is_default` = 1 WHERE `code` = 'ar';

-- إضافة اللغة الإنجليزية كاحتياط
INSERT IGNORE INTO `general_lang_list` (`ids`, `code`, `country_code`, `is_default`, `status`, `created`) VALUES
('en_lang_12345678901234567890123', 'en', 'US', 0, 1, NOW());

-- تنظيف الجلسات القديمة
DELETE FROM `general_sessions` WHERE `timestamp` < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));

-- رسالة تأكيد
SELECT 
    'تم إصلاح مشكلة تسجيل دخول الإدارة بنجاح!' as 'Status',
    '<EMAIL>' as 'البريد الإلكتروني',
    '123456' as 'كلمة المرور',
    'https://tikt0k.co/admin' as 'رابط الإدارة';
