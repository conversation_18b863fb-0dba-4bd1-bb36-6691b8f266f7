/**
 * ملف JavaScript للميزات المحسنة
 * يحتوي على جميع الوظائف التفاعلية للموقع
 */

$(document).ready(function() {
    
    // تهيئة الميزات
    initNotifications();
    initLiveChat();
    initCoupons();
    initReviews();
    initLoyaltySystem();
    
});

/**
 * نظام الإشعارات
 */
function initNotifications() {
    // التحقق من الإشعارات الجديدة كل 30 ثانية
    setInterval(checkNewNotifications, 30000);
    
    // وضع علامة مقروء عند النقر
    $(document).on('click', '.notification-item', function() {
        var notificationId = $(this).data('id');
        markNotificationAsRead(notificationId);
    });
    
    // وضع علامة مقروء على الكل
    $(document).on('click', '#mark-all-read', function() {
        markAllNotificationsAsRead();
    });
}

function checkNewNotifications() {
    $.ajax({
        url: base_url + 'notifications/ajax_get_notifications',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                updateNotificationBadge(response.unread_count);
                updateNotificationDropdown(response.notifications);
            }
        }
    });
}

function markNotificationAsRead(notificationId) {
    $.ajax({
        url: base_url + 'notifications/mark_as_read/' + notificationId,
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                $('#notification-' + notificationId).removeClass('unread');
                updateNotificationBadge();
            }
        }
    });
}

function updateNotificationBadge(count) {
    var badge = $('.notification-badge');
    if (count > 0) {
        badge.text(count).show();
    } else {
        badge.hide();
    }
}

/**
 * نظام الدردشة المباشرة
 */
function initLiveChat() {
    // فتح نافذة الدردشة
    $(document).on('click', '#open-chat', function() {
        $('#chat-window').toggle();
        if ($('#chat-window').is(':visible')) {
            loadChatMessages();
        }
    });
    
    // إرسال رسالة
    $(document).on('submit', '#chat-form', function(e) {
        e.preventDefault();
        sendChatMessage();
    });
    
    // التحقق من الرسائل الجديدة
    setInterval(checkNewChatMessages, 5000);
}

function sendChatMessage() {
    var message = $('#chat-message').val().trim();
    var conversationId = $('#conversation-id').val();
    
    if (message === '') return;
    
    $.ajax({
        url: base_url + 'live_chat/chat/send_message',
        type: 'POST',
        data: {
            conversation_id: conversationId,
            message: message
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                $('#chat-message').val('');
                appendChatMessage(message, 'user', response.timestamp);
            }
        }
    });
}

function loadChatMessages() {
    var conversationId = $('#conversation-id').val();
    
    $.ajax({
        url: base_url + 'live_chat/chat/get_messages/' + conversationId,
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                displayChatMessages(response.messages);
            }
        }
    });
}

/**
 * نظام الكوبونات
 */
function initCoupons() {
    // تطبيق كوبون
    $(document).on('click', '#apply-coupon', function() {
        applyCoupon();
    });
    
    // إزالة كوبون
    $(document).on('click', '#remove-coupon', function() {
        removeCoupon();
    });
}

function applyCoupon() {
    var couponCode = $('#coupon-code').val().trim();
    var orderAmount = parseFloat($('#order-amount').val());
    
    if (couponCode === '') {
        showAlert('يرجى إدخال كود الخصم', 'warning');
        return;
    }
    
    $.ajax({
        url: base_url + 'coupons/apply_coupon',
        type: 'POST',
        data: {
            coupon_code: couponCode,
            order_amount: orderAmount
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                updateOrderSummary(response.data);
                showAlert(response.message, 'success');
            } else {
                showAlert(response.message, 'error');
            }
        }
    });
}

function updateOrderSummary(data) {
    $('#discount-amount').text(data.discount_amount + ' ريال');
    $('#final-amount').text(data.final_amount + ' ريال');
    $('#coupon-applied').show();
    $('#coupon-form').hide();
}

/**
 * نظام التقييمات
 */
function initReviews() {
    // تقييم بالنجوم
    $(document).on('click', '.star-rating .star', function() {
        var rating = $(this).data('rating');
        setStarRating(rating);
    });
    
    // إرسال تقييم
    $(document).on('submit', '#review-form', function(e) {
        e.preventDefault();
        submitReview();
    });
    
    // الإعجاب بتقييم
    $(document).on('click', '.like-review', function() {
        var reviewId = $(this).data('review-id');
        likeReview(reviewId);
    });
}

function setStarRating(rating) {
    $('.star-rating .star').each(function(index) {
        if (index < rating) {
            $(this).addClass('active');
        } else {
            $(this).removeClass('active');
        }
    });
    $('#rating-value').val(rating);
}

function submitReview() {
    var formData = $('#review-form').serialize();
    
    $.ajax({
        url: base_url + 'reviews/add_review',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showAlert(response.message, 'success');
                $('#review-form')[0].reset();
                $('.star-rating .star').removeClass('active');
            } else {
                showAlert(response.message, 'error');
            }
        }
    });
}

function likeReview(reviewId) {
    $.ajax({
        url: base_url + 'reviews/like_review',
        type: 'POST',
        data: { review_id: reviewId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                var button = $('[data-review-id="' + reviewId + '"]');
                var icon = button.find('i');
                var count = button.find('.like-count');
                
                if (response.action === 'liked') {
                    icon.removeClass('far').addClass('fas');
                    button.addClass('liked');
                } else {
                    icon.removeClass('fas').addClass('far');
                    button.removeClass('liked');
                }
                
                count.text(response.likes_count);
            }
        }
    });
}

/**
 * نظام الولاء
 */
function initLoyaltySystem() {
    // استبدال مكافأة
    $(document).on('click', '.redeem-reward', function() {
        var rewardId = $(this).data('reward-id');
        redeemReward(rewardId);
    });
    
    // عرض تفاصيل المكافأة
    $(document).on('click', '.reward-details', function() {
        var rewardId = $(this).data('reward-id');
        showRewardDetails(rewardId);
    });
}

function redeemReward(rewardId) {
    // تأكيد الاستبدال
    if (!confirm('هل أنت متأكد من استبدال هذه المكافأة؟')) {
        return;
    }
    
    $.ajax({
        url: base_url + 'loyalty/redeem_reward',
        type: 'POST',
        data: { reward_id: rewardId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showAlert(response.message, 'success');
                updateUserPoints(response.remaining_points);
                $('[data-reward-id="' + rewardId + '"]').prop('disabled', true);
            } else {
                showAlert(response.message, 'error');
            }
        }
    });
}

function updateUserPoints(points) {
    $('.user-points').text(points);
}

/**
 * وظائف مساعدة
 */
function showAlert(message, type) {
    var alertClass = 'alert-info';
    
    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            break;
        case 'error':
            alertClass = 'alert-danger';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            break;
    }
    
    var alert = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message +
                '<button type="button" class="close" data-dismiss="alert">' +
                '<span>&times;</span>' +
                '</button>' +
                '</div>';
    
    $('#alerts-container').html(alert);
    
    // إخفاء التنبيه بعد 5 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

function formatCurrency(amount) {
    return parseFloat(amount).toFixed(2) + ' ريال';
}

function timeAgo(timestamp) {
    var now = new Date();
    var time = new Date(timestamp);
    var diff = Math.floor((now - time) / 1000);
    
    if (diff < 60) return 'منذ ' + diff + ' ثانية';
    if (diff < 3600) return 'منذ ' + Math.floor(diff / 60) + ' دقيقة';
    if (diff < 86400) return 'منذ ' + Math.floor(diff / 3600) + ' ساعة';
    return 'منذ ' + Math.floor(diff / 86400) + ' يوم';
}

// تحديث الوقت كل دقيقة
setInterval(function() {
    $('.time-ago').each(function() {
        var timestamp = $(this).data('timestamp');
        $(this).text(timeAgo(timestamp));
    });
}, 60000);

/**
 * تحسينات تجربة المستخدم
 */

// تحميل كسول للصور
$('img[data-src]').each(function() {
    var img = $(this);
    img.attr('src', img.data('src')).removeAttr('data-src');
});

// تأثيرات الحركة
$('.animate-on-scroll').each(function() {
    var element = $(this);
    $(window).scroll(function() {
        var elementTop = element.offset().top;
        var elementBottom = elementTop + element.outerHeight();
        var viewportTop = $(window).scrollTop();
        var viewportBottom = viewportTop + $(window).height();
        
        if (elementBottom > viewportTop && elementTop < viewportBottom) {
            element.addClass('animated');
        }
    });
});

// تحسين الأداء - تأخير التحميل
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تطبيق التأخير على البحث
$('#search-input').on('input', debounce(function() {
    performSearch($(this).val());
}, 300));

function performSearch(query) {
    if (query.length < 2) return;
    
    $.ajax({
        url: base_url + 'search/ajax_search',
        type: 'GET',
        data: { q: query },
        dataType: 'json',
        success: function(response) {
            displaySearchResults(response.results);
        }
    });
}
