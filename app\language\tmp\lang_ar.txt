{"reasons_why_you_should_try_our_panel": "Reasons why you should try our panel,", "let_us_help_you_build_your_online_presence_quickly_and_efficiently": "Let us help you build your online presence quickly and efficiently!                                        ", "best_quality": "Best quality", "the_highest_quality_smm_services_to_meet_your_needs": "The highest quality SMM services to meet your needs.", "diverse_payment_options": "Diverse payment options", "we_have_a_good_amount_of_different_payment_options": "We have a good amount of different payment options.", "super_quick_delivery": "Super quick delivery", "we_provide_automated_services_with_quick_delivery": "We provide automated services with quick delivery.", "comes_with_all_the_essential_features_and_elements_you_need_here_are_the_key_features_of_our_services_you_must_know": "Comes with all the essential features and elements you need, here are the key features of our services you must know.                                        ", "subscribe_us": "Subscribe Us", "get_your_social_accounts_followers_and_likes_at_one_place_instantly": "Get Your Social Account's Followers And Likes At One Place, Instantly                                        ", "what_people_say_about_us": "What People Say About Us?", "our_service_has_an_extensive_customer_roster_built_on_years_worth_of_trust_read_what_our_buyers_think_about_our_range_of_service": "Our service has an extensive customer roster built on years’ worth of trust. Read what our buyers think about our range of service.                                        ", "client_one": "<PERSON>", "client_one_jobname": "Youtuber", "client_one_comment": "After trying several websites who claim to have 'fast delivery', I'm glad I finally found this service. They literally started delivering 5 seconds after my payment!                                        ", "client_two": "<PERSON>", "client_two_jobname": "Instagram Model", "client_two_comment": "I cannot stress enough how happy I am with the service that I received. Thanks to all of you, my Instagram account is surging with activity! You’ve not only earned yourself a loyal customer, but a friend for life.                                        ", "client_three": "<PERSON><PERSON><PERSON>", "client_three_jobname": "<PERSON><PERSON><PERSON>", "client_three_comment": "Wow! This is amazing, i have been purchasing Instagram Likes for over a year and never got a delay! ? did a great job always                                        ", "we_have_several_services_that_you_can_opt_for_backed_by_our_comprehensive_guarantee_click_the_button_below_to_find_out_more": "We have several services that you can opt for backed by our comprehensive guarantee – click the button below to find out more.                                        ", "dashboard": "Dashboard", "boost_your_social_media_marketing": "Boost Your Social Media Marketing", "header_top_desc": "From Unexpected Friendships, to Lasting Relationships. Our website is a Cheap SMM and SEO service Reseller Auto Panel Script. Fast, Reliable and Secure, offering World Best Quality and Cheapest Automatic Social Media Services which is specially developed for Resellers with High Speed order completion!.                                        ", "why_choose_us": "WHY CHOOSE US?", "we_make_your_life_easier_by_boosting_sales": "We Make Your Life Easier By Boosting Sales", "support_only_247": "Support Only 24/7", "we_are_proud_to_have_the_most_reliable_or_fastest_support_in_the_smm_world_panel_replying_to_your_tickets_247": "We are proud to have the most reliable or fastest support in the SMM World Panel, replying to your tickets 24/7.                                        ", "delivering_within_a_minutes": "Delivering Within a Minutes", "our_delivery_is_automated_and_it_takes_minutes_if_not_seconds_to_fulfil_orders": "Our delivery is automated, and it takes minutes if not seconds to fulfil orders.                                        ", "unbelievable_prices": "Unbelievable Prices", "our_prices_most_reasonable_in_the_market_starting_from_at_0001": "Our prices most reasonable in the market, starting from at $0.001.                                        ", "how_it_works": "How it works?", "by_following_the_processes_below_you_can_make_any_order_you_want": "By following the processes below you can make any order you want.                                        ", "register_and_log_in": "Register & log in", "creating_an_account_is_the_first_step_then_you_need_to_log_in": "Creating an account is the first step, then you need to log in.", "add_funds": "Add funds", "next_pick_a_payment_method_and_add_funds_to_your_account": "Next, pick a payment method and add funds to your account.", "select_a_service": "Select a service", "select_the_services_you_want_and_get_ready_to_receive_more_publicity": "Select the services you want and get ready to receive more publicity.                                        ", "enjoy_superb_results": "Enjoy superb results", "you_can_enjoy_incredible_results_when_your_order_is_complete": "You can enjoy incredible results when your order is complete.", "what_we_offer_for_your_succes_brand": "What We Offer For Your Succes Brand", "we_are_active_for_support_only_24_hours_a_day_and_seven_times_a_week_with_all_of_your_demands_and_services_around_the_day_dont_go_anywhere_else___we_are_here_ready_to_serve_you_and_help_you_with_all_of_your_smm_needs_users_or_clients_with_smm_orders_and_in_need_of_cheap_smm_services_are_more_then_welcome_in_our_smm_panel": "We are active for support only 24 hours a day and seven times a week with all of your demands and services around the day. Don't go anywhere else. <br> <br> We are here ready to serve you and help you with all of your SMM needs. Users or Clients with SMM orders and in need of CHEAP SMM services are more then welcome in our SMM PANEL.                                        ", "we_answered_some_of_the_most_frequently_asked_questions_on_our_panel": "We answered some of the most frequently asked questions on our panel.                                        ", "smm_panels__what_are_they": "SMM panels - what are they?", "an_smm_panel_is_an_online_shop_that_you_can_visit_to_puchase_smm_services_at_great_prices": "An SMM panel is an online shop that you can visit to puchase SMM services at great prices.                                        ", "what_smm_services_can_i_find_on_this_panel": "What SMM services can I find on this panel?", "we_sell_different_types_of_smm_services__likes_followers_views_etc": "We sell different types of SMM services — likes, followers, views, etc.                                        ", "are_smm_services_on_your_panel_safe_to_buy": "Are SMM services on your panel safe to buy?", "sure_your_accounts_wont_get_banned": "Sure! Your accounts won't get banned.", "how_does_a_mass_order_work": "How does a mass order work?", "its_possible_to_place_multiple_orders_with_different_links_at_once_with_the_help_of_the_mass_order_feature": "It's possible to place multiple orders with different links at once with the help of the mass order feature.                                        ", "what_does_dripfeed_mean": "What does Drip-feed mean?", "grow_your_accounts_as_fast_as_you_want_with_the_help_of_dripfeed_how_it_works_lets_say_you_want_2000_likes_on_your_post_instead_of_getting_all_2000_at_once_you_can_get_200_each_day_for_10_days": "Grow your accounts as fast as you want with the help of Drip-feed. How it works: let's say you want 2000 likes on your post. Instead of getting all 2000 at once, you can get 200 each day for 10 days.                                        ", "all_user_information_is_kept_100_private_and_will_not_be_shared_with_anyone_always_remember_you_are_protected_with_our_panel__most_trusted_smm_panel": "All user information is kept 100% private and will NOT be shared with anyone. Always remember, you are protected with our panel - Most trusted smm panel                                        ", "statistics": "Statistics", "services": "Services", "order": "Order", "order_logs": "Order Logs", "new_order": "New Order", "api": "API", "user_manager": "User manager", "subscribers": "Subscribers", "user_activity_logs": "User Activity Logs", "banned_ip_address_list": "Banned IP Address List", "system_settings": "System Settings", "api_providers": "API Providers", "language": "Language", "documentation": "Documentation", "support": "Support", "profile": "Profile", "admin_account": "Admin account", "add_money": "Add money", "hi": "Hi", "enter_license": "Enter license", "payments": "Payments", "payment_bonuses": "Payment Bonuses", "quick_links": "Quick Links", "terms__conditions": "Terms & Conditions", "cookie_policy": "<PERSON><PERSON>", "home": "Home", "copyright": "Copyright © 2019", "add_new": "Add new", "lists": "Lists", "no_": "No.", "created": "Created", "updated": "Updated", "status": "Status", "action": "Action", "description": "Description", "edit": "Edit", "delete": "Delete", "active": "Active", "deactive": "Deactive", "yes": "Yes", "no": "No", "email": "E-mail", "timezone": "Time zone", "password": "Password", "confirm_password": "Confirm Password", "save": "Save", "look_like_there_are_no_results_in_here": "Look like there are no results in here!", "subject": "Subject", "content": "Content", "message": "Message", "submit": "Submit", "cancel": "Cancel", "password_is_required": "Password is required", "email_is_required": "Email is required", "invalid_email_format": "Invalid email format", "password_must_be_at_least_6_characters_long": "Password must be at least 6 characters long", "password_does_not_match_the_confirm_password": "Password does not match the confirm password", "there_was_an_error_processing_your_request_please_try_again_later": "There was an error processing your request. Please try again later                                        ", "update_successfully": "Update successfully", "deleted_successfully": "Deleted successfully", "the_item_does_not_exist_please_try_again": "The item does not exist. Please try again", "are_you_sure_you_want_to_delete_this_item": "Are you sure you want to delete this item?", "are_you_sure_you_want_to_delete_all_items": "Are you sure you want to delete all items?", "please_choose_at_least_one_item": "Please choose at least one item", "search_for_": "Search for...", "sign_out": "Sign out", "sign_up": "Sign Up", "login": "<PERSON><PERSON>", "note": "Note:", "facebook": "Facebook", "instagram": "Instagram", "pinterest": "Pinterest", "twitter": "Twitter", "paypal": "<PERSON><PERSON>", "2checkout": "2Checkout", "stripe": "Stripe", "users": "Users", "admin": "Admin", "regular_user": "Regular User", "funds": "Funds", "user_profile": "User profile", "send_mail": "Send Mail", "edit_user": "Edit user", "basic_information": "Basic Information", "first_name": "First name", "last_name": "Last name", "account_type": "Account type", "note_if_you_dont_want_to_change_password_then_leave_these_password_fields_empty": "Note: If you don't want to change password then leave these password fields empty!                                        ", "more_informations": "More Informations", "whatsapp_number": "WhatsApp Number", "website": "Website", "phone": "Phone", "skype_id": "Skype ID", "address": "Address", "note_if_you_dont_want_add_more_information_then_leave_these_informations_fields_empty": "Note: If you don't want add more information then leave these informations fields empty!                                        ", "to": "To", "please_fill_in_the_required_fields": "Please fill in the required fields", "an_account_for_the_specified_email_address_already_exists_try_another_email_address": "An account for the specified email address already exists. Try another email address                                        ", "subject_is_required": "Subject is required", "message_is_required": "Message is required", "description_is_required": "Description is required", "your_email_has_been_successfully_sent_to_user": "Your email has been successfully sent to user", "the_account_does_not_exists": "The account does not exists", "the_input_value_was_not_a_correct_number": "The input value was not a correct number", "can_not_delete_administrator_account": "Can not delete Administrator account", "custom_rate": "Custom rate(%)", "history_ip": "history_ip", "view_user": "View User", "back_to_admin": "Back to Admin", "edit_custom_rates": "Edit custom rates", "add_custom_rate": "Add Custom Rate", "delete_all": "Delete all", "allowed_payment_methods": "Allowed payment methods:", "settings": "Settings", "general_settings": "General Settings", "website_setting": "WebSite Setting", "logo": "Website Logo", "terms__policy_page": "Terms & Policy page", "default_setting": "<PERSON><PERSON><PERSON>", "other": "Other", "email_setting": "<PERSON>ail <PERSON>ting", "email_template": "<PERSON>ail Te<PERSON>late", "integrations": "Integrations", "payment": "Payment", "maintenance_mode": "Maintenance mode", "link_to_access_the_maintenance_mode": "Make sure you remmeber this link to get access Maintenance mode before you activate:                                        ", "website_name": "Website name", "website_description": "Website description", "website_keywords": "Website keywords", "website_title": "Website title", "website_logo": "Website Logo", "website_favicon": "Website favicon", "website_logo_white": "Website logo (white)", "terms__policy": "Terms & Policy", "content_of_terms": "Content of Terms", "content_of_policy": "Content of Policy", "other_settings": "Other settings", "enable_https": "Enable HTTPS", "emded_code": "Emded Code", "social_media_links": "Social Media links", "header_menu_skin_colors": "Header <PERSON><PERSON> Colors", "disable_signup_page": "Disable Signup Page", "note_please_make_sure_the_ssl_certificate_has_the_active_status_in_your_hosting_before__you_activate": "Note: Please make sure the SSL certificate has the 'Active' status in your hosting before  you activate.                                        ", "note_only_supports_javascript_code": "Note: Only supports Javascript code", "contact_informations": "Contact Informations", "working_hour": "Working Hour", "tel": "Tel", "email_notifications": "Email notifications", "new_user_welcome_email": "New User Welcome Email", "new_user_notification_email": "New User Notification Email", "receive_notification_when_a_new_user_registers_to_the_site": "(Receive notification when a new user registers to the site)", "payment_notification_email": "Payment Notification Email", "send_notification_when_a_new_user_add_funds_successfully_to_user_balance": "(Send notification when a new user add funds successfully to user balance)                                        ", "ticket_notification_email": "Ticket Notification Email", "send_notification_to_user_when_admin_reply_to_a_ticket": "(Send notification to user when Admin reply to a ticket)", "send_notification_to_admin_when_user_open_a_ticket": "(Send notification to Admin when user open a ticket)", "order_notification_email": "Order Notification Email", "receive_notification_when_a_user_place_order_successfully": "(Receive notification when a user place order successfully)", "from": "From", "your_name": "Your name", "email_protocol": "Email protocol", "php_mail_function": "PHP mail function", "recommended": "(Recommended)", "sometime_email_is_going_into__recipients_spam_folders_if_php_mail_function_is_enabled": "Sometime, email is going into  recipients' spam folders if PHP mail function is enabled                                        ", "smtp": "SMTP", "smtp_server": "SMTP Server", "smtp_port": "SMTP Port", "smtp_encryption": "SMTP Encryption", "smtp_username": "SMTP Username", "smtp_password": "SMTP Password", "password_recovery": "Password Recovery", "you_can_use_following_template_tags_within_the_message_template": "You can use following template tags within the message template:                                        ", "displays_the_users_first_name": "displays the user's first name", "displays_the_users_last_name": "displays the user's last name", "displays_the_users_email": "displays the user's email", "displays_the_users_timezone": "displays the user's timezone", "displays_recovery_password_link": "displays recovery password link", "payment_integration": "Payment Integration", "currency_setting": "<PERSON><PERSON><PERSON><PERSON>", "currency_code": "Currency Code", "thousand_separator": "Thousand Separator", "decimal_separator": "Decimal Separator", "dot": "Dot", "comma": "Comma", "space": "Space", "auto_currency_converter": "Auto Currency converter", "applying_when_you_fetch_sync_all_services_from_smm_providers": "(Applying when you fetch, sync all services from SMM providers)", "1_original_currency": "1 Original currency", "new_currency": "New Currency", "if_you_dont_want_to_change_currency_rate_then_leave_this_currency_rate_field_to_1": "If you don't want to change Currency rate then leave this currency rate field to 1                                        ", "the_paypal_payments_only_supports_these_currencies": "The PayPal Payments only supports these currencies:", "currency_symbol": "Currency Symbol", "transaction_limits": "Transaction Limits", "currency_decimal_places": "Currency decimal places", "minimum_amount": "Minimum Amount", "environment": "Environment", "live": "Live", "transaction_fee": "Transaction fee", "sandbox_test": "Sandbox (test)", "paypal_client_id": "Paypal Client ID", "paypal_client_secret": "Paypal Client Secret", "publishable_key": "Publishable Key", "secret_key": "Secret Key", "private_key": "Private Key", "2checkout_account_number_sellerid": "2Checkout account number (sellerId)", "auto_clear_ticket_lists": "Auto clear ticket lists", "default_tickets_log": "De<PERSON>ult Tick<PERSON> log", "clear_ticket_lists_after_x_days_without_any_response_from_user": "Clear Ticket lists (after X days) without any response from user                                        ", "default_service": "Default Service", "default_min_order": "Default Min Order", "default_max_order": "Default Max Order", "default_price_per_1000": "Default Price per 1000", "dripfeed_option": "Drip-feed option", "note_please_make_sure_the_dripfeed_feature_has_the_active_status_in_api_provider_before_you_activate": "Note: Please make sure the Drip-feed feature has the 'Active' status in API provider before you activate.                                        ", "default_runs": "Default Runs", "default_interval_in_minutes": "De<PERSON><PERSON> (in minutes)", "explication_of_the_service_symbol": "Explication of the service symbol", "pagination": "Pagination", "limit_the_maximum_number_of_rows_per_page": "Limit the Maximum Number of Rows per Page", "price_percentage_increase": "Price percentage increase", "use_for_sync_and_bulk_add_services": "Use for sync and Bulk add services", "displays_the_service_lists_without_login_or_register": "Displays the service lists without login or register", "displays_api_tab_in_header": "Displays API tab in header", "displays_required_skypeid_field_in_signup_page": "Displays required SkypeID field in signup page", "displays_google_recapcha": "Displays Google reCAPTCHA", "google_recaptcha_site_key": "Google reCAPTCHA site key", "google_recaptcha_serect_key": "Google reCAPTCHA serect key", "please_verify_recaptcha": "Please verify reCAPTCHA", "email_verification_for_new_customer_accounts": "Email verification for new customer accounts", "email_verification_for_new_customer_accounts_preventing_spam_account": "Email verification for new customer accounts (Preventing Spam Account)                                        ", "default_timezone": "Default Timezone", "set_the_default_timezone_at_register_page": "Set the default timezone at Register page", "notification_popup_at_home_page": "Notification popup at home page", "disable_home_page_langding_page": "Disable Home page (Langding page)", "default_homepage": "Default Homepage", "language_code": "Language code", "choose_a_language_code": "Choose a language code", "default": "<PERSON><PERSON><PERSON>", "location": "Location", "key": "Key", "value": "Value", "name": "Name", "code": "Code", "icon": "Icon", "choose_your_country": "Choose your country", "translation_editor": "Translation editor", "language_code_does_not_exists": "Language Code does not exists", "language_code_already_exists": "Language code already exists", "transaction_logs": "Transaction logs", "user": "User", "transaction_id": "Transaction ID", "payment_method": "Payment method", "amount_includes_fee": "Amount (includes fee)", "amount_paid_includes_fee": "Amount Paid (includes fee)", "paid": "Paid", "waiting_for_buyer_funds": "Waiting for buyer funds...", "cancelled_timed_out": "Cancelled/Timed Out", "tickets": "Tickets", "mark_as_new": "<PERSON> as <PERSON>", "mark_as_pending": "<PERSON> as Pending", "mark_as_closed": "<PERSON> as Closed", "add_new_ticket": "Add New Ticket", "ticket_no": "Ticket #", "submit_as_closed": "Submit as Closed", "submit_as_pending": "Submit as Pending", "submit_as_new": "Submit as New", "new": "New", "pending": "Pending", "closed": "Closed", "answered": "Answered", "ticket_created_successfully": "Ticket created successfully", "cancellation": "Cancellation", "speed_up": "Speed Up", "refill": "Refill", "unread": "Unread", "request": "Request", "enter_the_transaction_id": "Enter the Transaction ID", "for_multiple_orders_please_separate_them_using_comma_example_123451234512345": "For multiple orders, please separate them using comma. (example: 12345,12345,12345)                                        ", "order_id_field_is_required": "Order ID field is required", "please_choose_a_request": "Please choose a request", "transaction_id_field_is_required": "Transaction ID field is required", "please_choose_a_payment_type": "Please choose a payment type", "faqs": "FAQs", "question": "Question", "answer": "Answer", "default_sorting_number": "Default Sorting number", "sorting": "Sort", "edit_faq": "Edit FAQ", "question_is_required": "Question is required", "answer_is_required": "Answer is required", "sort_number_must_to_be_greater_than_zero": "Sort number must to be greater than zero", "api_documentation": "API Documentation", "note_please_read_the_api_intructions_carefully_its_your_solo_responsability_what_you_add_by_our_api": "Note: Please read the API intructions carefully. Its your solo responsability what you add by our API.                                        ", "response_format": "Response format", "http_method": "HTTP Method", "api_key": "API Key", "download_php_code_examples": "Download PHP Code Examples", "place_new_order": "Place new Order", "example_response": "Example response:", "status_order": "Status Order", "parameter": "Parameter", "multiple_orders_status": "Multiple orders status", "services_lists": "Services Lists", "balance": "Balance", "your_api_key": "Your API key", "service_id": "Service ID", "link_to_page": "Link to page", "needed_quantity": "Needed quantity", "order_id": "Order ID", "order_ids_separated_by_comma_array_data": "Order IDs separated by comma (array data)", "api_is_disable_for_this_user_or_user_not_found_contact_the_support": "API is Disable for this user or User Not Found! Contact the Support                                        ", "this_action_is_invalid": "This action is Invalid", "there_are_missing_required_parameters_please_check_your_api_manual": "There are missing required parameters. Please check your API Manual                                        ", "invalid_link": "Invalid Link", "service_id_does_not_exists": "Service ID does not exists", "quantity_must_to_be_greater_than_or_equal_to_minimum_amount": "Quantity must to be greater than or equal to minimum amount", "quantity_must_to_be_less_than_or_equal_to_maximum_amount": "Quantity must to be less than or equal to maximum amount", "not_enough_funds_on_balance": "Not enough funds on balance", "order_id_is_required_parameter_please_check_your_api_manual": "Order ID is required parameter. Please check your API Manual", "incorrect_order_id": "Incorrect order ID", "edit_service": "Edit Service", "package_name": "Package Name", "choose_a_category": "Choose a category", "maximum_amount": "Maximum Amount", "price": "Price", "rate_per_1000": "Rate per 1000", "min__max_order": "Min / Max order", "name_is_required": "Name is required", "category_is_required": "Category is required", "min_order_is_required": "Min order is required", "max_order_is_required": "Max order is required", "max_order_must_to_be_greater_than_min_order": "Max order must to be greater than Min order", "price_invalid": "Price invalid", "currency_decimal_places_must_to_be_equal_than_2": "Currency decimal places must to be equal than 2", "details": "Details", "__good_seller": "<PERSON> Seller", "__speed_level": "Speed Level", "__hot_service": "Hot service", "__best_service": "Best Service", "__drip_feed": "<PERSON><PERSON>", "__cancel_button": "<PERSON><PERSON>", "import_services": "Import Services", "actions": "Actions", "custom_comments": "Custom comments", "custom_comments_package": "Custom comments package", "mentions_with_hashtags": "Mentions with hashtags", "mentions_custom_list": "Mentions custom list", "mentions_hashtag": "Mentions hashtag", "mentions_user_followers": "Mentions user_followers", "mentions_media_likers": "Mentions_media_likers", "package": "Package", "comment_likes": "Comment likes", "all_deactivated_services": "All deactivated Services", "failed_to_delete_there_are_no_deactivate_service_now": "Failed to delete. There are no deactivate service now!", "category": "Category", "edit_category": "Edit Category", "all_deactivated_categories": "All deactivated Categories", "failed_to_delete_there_are_no_deactivate_category_now": "Failed to delete. There are no deactivate Category now!", "single_order": "Single Order", "mass_order": "Mass Order", "order_service": "Order Service", "choose_a_service": "Choose a service", "link": "Link", "quantity": "Quantity", "yes_i_have_confirmed_the_order": "Yes, i have confirmed the order!", "total_charge": "Total Charge:", "order_resume": "Order Resume", "service_name": "Service name", "price_per_1000": "Price per 1000", "place_order": "Place order", "one_order_per_line_in_format": "One order per line in format", "here_you_can_place_your_orders_easy_please_make_sure_you_check_all_the_prices_and_delivery_times_before_you_place_a_order_after_a_order_submited_it_cannot_be_canceled": "Here you can place your orders easy! Please make sure you check all the prices and delivery times before you place a order! After a order submited it cannot be canceled.                                        ", "failed": "Failed!", "there_was_some_issues_with_your_mass_order": "There was some issues with your mass order:", "order_content": "Order content", "error_message": "Error Message", "order_basic_details": "Order Basic Details", "sort_by": "Sort by", "all": "All", "completed": "Completed", "awaiting": "Awaiting", "processing": "Processing", "in_progress": "In progress", "partial": "Partial", "error": "Error", "canceled": "Canceled", "refunded": "Refunded", "edit_order": "Edit Order", "start_counter": "Start counter", "remains": "<PERSON><PERSON><PERSON>", "amount": "Amount", "charge": "Charge", "service": "Service", "service_does_not_exists": "Service does not exists", "order_amount_exceeds_available_funds": "Order amount exceeds available funds!", "order_amount_exceeds_available_the_min_max": "Order amount exceeds available minimum or maximum!", "please_choose_a_category": "Please choose a category", "please_choose_a_service": "Please choose a service", "category_does_not_exists": "Category does not exists", "quantity_is_required": "Quantity is required", "you_must_confirm_to_the_conditions_before_place_order": "You must confirm to the conditions before place order", "place_order_successfully": "Place Order successfully", "field_cannot_be_blank": "Field cannot be blank", "you_do_not_have_enough_funds_to_place_order": "You do not have enough funds to Place order", "invalid_format_place_order": "Invalid format place order", "link_is_required": "Link is required", "start_counter_is_a_number_format": "Start counter is a number format", "remains_is_a_number_format": "Remains is a number format", "dripfeed": "Drip-feed", "what_is_dripfeed": "What is Drip-feed?", "runs": "Runs", "interval_in_minutes": "Interval (in minutes)", "interval": "Interval", "total_quantity": "Total Quantity", "runs_is_required": "Runs is required", "interval_time_is_required": "Interval time is required", "interval_time_must_to_be_less_than_or_equal_to_60_minutes": "Interval time must to be less than or equal to 60 minutes", "drip_feed_desc": "<p><strong>Drip-Feed</strong> is a service that we are offering so you would be able to put the same order multiple times automatically.</p>\r\n                        <p>Example: let's say you want to get 1000 likes on your Instagram Post but you want to get 100 likes each 30 minutes, you will put:</p>\r\n                        <ul>\r\n                          <li>Link: Your Post Link</li>\r\n                          <li>Quantity: 100 </li>\r\n                          <li>Runs: 10</li>\r\n                          <li>Interval: 30</li>\r\n                        </ul>\r\n                        <p>\r\n                          <strong>Note:</strong> Never order more quantity than the maximum which is written on the service name (Quantity x Runs), Example if the service's max is 4000, you don’t put Quantity: 500 and Run: 10, because total quantity will be 500x10 = 5000 which is bigger than the service max (4000). Also never put the Interval below the actual start time (some services need 60 minutes to start, don’t put Interval less than the service start time or it will cause a fail in your order).\r\n                        </p>                                        ", "comments": "Comments", "usernames": "Usernames", "hashtag": "Hashtag", "media_url": "Media Url", "hashtags_format_hashtag": "Hashtags (Format: #hashtag)", "hashtag_field_is_required": "Hashtag field is required", "username_field_is_required": "Username field is required", "comments_field_is_required": "Comments field is required", "min_cannot_be_higher_than_max": "Min cannot be higher than Max", "incorrect_delay": "Incorrect delay", "min": "min", "max": "max", "minimum_1_post": "minimum 1 post", "new_posts_future_posts_must_to_be_greater_than_or__equal_to_1": "New Posts (Future posts) must to be greater than or  equal to 1", "1_per_line": "(1 per line)", "subscriptions": "Subscriptions", "no_delay": "No delay", "minutes": "minutes", "posts": "Posts", "new_posts": "New posts", "actived_posts": "Actived Posts", "username": "Username", "expiry": "Expiry", "delay": "Delay", "paused": "Paused", "expired": "Expiry field is required", "total_users": "Total Users", "your_balance": "Your Balance", "total_orders": "Total Orders", "total_tickets": "Total Tickets", "total_transactions": "Total Transactions", "recent_orders": "Recent Orders", "recent_tickets": "Recent Tickets", "total_amount_recieved": "Total Amount Recieved", "total_amount_spent": "Total Amount Spent", "total_users_balance": "Total Users' Balance", "total_providers_balance": "Total Providers' balance", "total_profit_30_days": "Total Profit 30 days", "total_profit_today": "Total Profit Today", "last_5_orders": "Last 5 Orders", "top_newest_users": "Top newest Users", "last_5_newest_users": "Last 5 Newest Users", "top_bestsellers": "Top bestsellers", "your_account": "Your account", "generate_new": "Generate new", "manual_payment": "Manual Payment", "minimal_payment": "Minimal payment", "you_can_deposit_funds_with_paypal_they_will_be_automaticly_added_into_your_account": "You can deposit funds with %s® they will be automaticly added into your account!                                        ", "amount_usd": "Amount (%s)", "yes_i_understand_after_the_funds_added_i_will_not_ask_fraudulent_dispute_or_chargeback": "Yes, I understand after the funds added i will not ask fraudulent dispute or charge-back!                                        ", "this_payment_gateway_is_not_already_active_at_the_present": "This Payment Gateway is not already active at the present!", "pay": "Pay", "you_can_make_a_manual_payment_to_cover_an_outstanding_balance_you_can_use_any_payment_method_in_your_billing_account_for_manual_once_done_open_a_ticket_and_contact_with_administrator": "You can make a manual payment to cover an outstanding balance. Once time, open a ticket and contact with Administrator.                                        ", "amount_is_required": "Amount is required", "amount_must_be_greater_than_zero": "Amount must be greater than zero", "minimum_amount_is": "Minimum Amount is", "you_must_confirm_to_the_conditions_before_paying": "You must confirm to the conditions before paying", "processing_": "Processing ....!", "payment_sucessfully": "Payment sucessfully!", "your_payment_has_been_processed_here_are_the_details_of_this_transaction_for_your_reference": "Your payment has been processed. Here are the details of this transaction for your reference:                                        ", "payment_unsucessfully": "Payment unsucessfully!", "sorry_your_payment_failed_no_charges_were_made": "Sorry, your payment failed. No charges were made", "2checkout_creditdebit_card_payment": "2Checkout Credit/Debit card Payment", "stripe_creditdebit_card_payment": "Stripe Credit/Debit card Payment", "user_information": "User information", "card_number": "CARD NUMBER", "expiry_date": "EXPIRY DATE", "there_is_no_any_payment_gateway_at_the_present": "There is no any payment gateway at the present!", "payment_gateway": "Payment Gateway", "empty": "Empty", "transaction_id_was_sent_to_your_email": "(Transaction ID was sent to your email)", "total_amount_xx_includes_fee": "Total Amount (%s) (Includes fee):", "currency_rate": "Currency Rate", "please_do_not_refresh_this_page": "Please do not refresh this page...", "deposit_to_": "Deposit_to_", "clicking_return_to_shop_merchant_after_payment_successfully_completed": "Clicking <strong class='text-danger'>Return to Shop (Merchant)</strong> after payment successfully completed                                        ", "user_can_send_a_maximum_of_each_payment_worth": "User can send a maximum of each payment worth", "paypal_transaction_fees_applies": "PayPal Transaction Fees applies", "more_details": "(More details)", "this_payment_is_for_services_and_it_is_not_refundable": "THIS PAYMENT IS FOR SERVICES AND IT IS NOT REFUNDABLE", "to_send_more_than__all_users_need_to_open_a_new_ticket": "To Send More, All Users need to open a new ticket.", "resellers_1_destination_for_smm_services": "Resellers' #1 Destination for SMM Services", "save_time_managing_your_social_account_in_one_panel_where_people_buy_smm_services_such_as_facebook_ads_management_instagram_youtube_twitter_soundcloud_website_ads_and_many_more": "Save time managing your social account in one panel. Where people buy SMM services such as Facebook ads management, Instagram, YouTube, Twitter, Soundcloud, Website ads and many more!                                        ", "get_start_now": "Get start now!", "best_smm_marketing_services": "Best SMM Marketing Services!", "best_smm_marketing_services_desc": "We provide the cheapest SMM Reseller Panel services amongst our competitors. If you’re looking for a super-easy way to offer additional marketing services to your existing and new clients, look no further! our site offers that and more ! <br><br>You can resell our services in any site or Link your site through API and start resell our services directly start building stronger relationships, and helping you make a great profit at the same time. We do the work so you can focus on what you do best! As you grow, your profit grows without having to hire more people. This allows you to expand your business without all the expense and headaches usually associated with growing bigger!                                        ", "what_we_offer": "What we offer!", "you_can_resell_our_services_and_grow_your_profit_easily_resellers_are_important_part_of_smm_panel": "You can resell our services and grow your profit easily, Resellers are important part of SMM PANEL                                        ", "technical_support_for_all_our_services_247_to_help_you": "Technical support for all our services 24/7 to help you", "get_the_best_high_quality_services_and_in_less_time_here": "Get the best high quality services and in less time here", "services_are_updated_daily_in_order_to_be_further_improved_and_to_provide_you_with_best_experience": "Services are updated daily In order to be further improved and to provide you with best experience                                        ", "we_have_api_support_for_panel_owners_so_you_can_resell_our_services_easily": "We have API Support For panel owners so you can resell our services easily                                        ", "we_have_a_popular_methods_as_paypal_and_many_more_can_be_enabled_upon_request": "We have a Popular methods as PayPal and many more can be enabled upon request                                        ", "resellers": "Resellers", "secure_payments": "Secure Payments", "supports": "Supports", "updates": "Updates", "api_support": "Api support", "high_quality_services": "High quality services", "ready_to_start_with_us": "READY TO START WITH US?", "terms__privacy_policy": "Terms & Privacy Policy", "terms": "Terms", "privacy_policy": "Privacy Policy", "notification": "Notification!", "close": "Close", "register_and_try_for_free_we_give_you_1_to_get_started": "Register and try for FREE. We give you € 1 to get started!", "login_to_your_account": "Login to your account", "only_letters_and_white_space_allowed": "Only letters and white space allowed", "remember_me": "Remember me", "forgot_password": "Forgot password", "dont_have_account_yet": "Don't have account yet?", "enter_your_registration_email_address_to_receive_password_reset_instructions": "Enter your registration email address to receive password reset instructions.                                        ", "new_password": "New Password", "register_now": "Register Now", "create_new_account": "Create new account", "i_agree_the": "I agree the", "already_have_account": "Already have account?", "oops_you_must_agree_with_the_terms_of_services_or_privacy_policy": "Oops! You must agree with the Terms of Services or Privacy Policy                                        ", "welcome_you_have_signed_up_successfully": "Welcome! you have signed up successfully.", "your_account_has_not_been_activated": "Your account has not been activated", "login_successfully": "<PERSON><PERSON> successfully", "email_address_and_password_that_you_entered_doesnt_match_any_account_please_check_your_account_again": "Email address and password that You entered doesn't match any account. Please check your account again                                        ", "we_have_send_you_a_link_to_reset_password_and_get_back_into_your_account_please_check_your_email": "We have send you a link to reset password and get back into your account. Please check your email                                        ", "your_password_has_been_successfully_changed": "Your password has been successfully changed", "thank_you_for_signing_up_please_check_your_email_to_complete_the_account_verification_process": "Thank you for signing up! Please check your email to complete the Account Verification Process                                        ", "congratulations_your_registration_is_now_complete": "Congratulations! Your Registration is Now Complete", "congratulations_desc": "Welcome to our service! We're happy to have you as a part of our community. Your account has been successfully created. You can access your account by clicking on the button below.                                        ", "api_providers_list": "API Providers List", "update_api": "Update API", "update_balance": "Update Balance", "type": "Type", "manual": "Manual", "edit_api": "Edit API", "api_url": "API Url", "list_of_api_services": "List of API Services", "choose_a_api_provider": "Choose a API Provider", "add_service": "Add service", "services_list_via_api": "Services list via API", "api_provider_does_not_exists": "API Provider does not exists.", "api_url_is_required": "API URL is required", "api_key_is_required": "API KEY is required", "sorry_the_service_id_already_exists": "Sorry! The Service ID already exists", "add_new_service_via_api": "Add New Service via API", "api_orderid": "API OrderID", "api_response": "API Response", "bulk_add_all_services": "Bulk Add All Services", "api_provider_name": "API Provider Name", "api_provider": "API Provider", "api_service_id": "API ServiceID", "price_percentage_increase_auto_rounding_to_2_decimal_places": "Price percentage increase (Auto rounding to 2 decimal places)", "bulk_add_limit": "Bulk add limit", "note_when_you_use_this_feature_the_system_will_bulk_add_services_categories_from_api_provider_and_set_price_percentage_increase": "Note: When you use this feature, the system will bulk add services, categories from API provider and set price percentage increase                                        ", "price_percentage_increase_in_invalid_format": "Price Percentage increase in invalid format", "bulk_add_limit_in_invalid_format": "Bulk add limit in invalid format", "add_edit_provider_note_desc": "Note: This script supports most of all API Providers (API v2) like: vinasmm.com, hqsmartpanel.com etc. So it doesn't support another API provider which have different API Parameters                                        ", "sync_services": "Sync Services", "disabled": "Disabled", "synchronization_results": "Synchronization results", "synchronous_request": "Synchronous request", "current_service": "Current Services", "current_service_sync_all_the_current_services": "Current Service: Sync all the current services", "all_auto_add_new_service_if_the_service_doesnt_exists": "All: Auto add new service if the service doesn't exists", "add_update_service": "Add/Update service", "service_lists_are_empty_unable_to_sync_services": "Service lists are empty. Unable to sync services!", "there_seems_to_be_an_issue_connecting_to_api_provider_please_check_api_key_and_token_again": "There seems to be an issue connecting to API provider. Please check API key and Token again!                                        ", "price_invalid_format": "Price invalid format", "auto_rounding_to_x_decimal_places": "(Auto rounding to %s decimal places)", "sync_min_max_dripfeed": "<PERSON><PERSON> <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>", "sync_new_price": "Sync New Price", "sync_original_price": "Sync Original Price", "auto_convert_to_new_currency_with_currency_rate_like_in": "Auto convert to new currency with currency rate like in", "currency_setting_page": "Currency Setting page", "auto_sync_services_setting": "Auto Sync Services Setting", "sync_service_name": "Sync Service Name", "sync_services_description_only_support_hq_smartpanel": "Sync Services Description (Only Support HQ SmartPanel)", "login_to_maintenace_mode": "Login to Maintenace Mode", "use_admin_account": "(Use Admin account)", "the_website_is_in_maintenance_mode": "The website is in maintenance mode", "were_undergoing_a_bit_of_scheduled_maintenance_sorry_for_the_inconvenience_well_be_backup_and_running_as_fast_as_possible": "We're undergoing a bit of scheduled maintenance. Sorry for the inconvenience. We'll be backup and running as fast as possible!                                        ", "displays_news__announcement_feature": "Displays News & Announcement feature", "news__announcement": "News & Announcement", "new_services": "New services", "updated_services": "Updated service", "announcement": "Announcement", "disabled_services": "Disabled services", "view": "View", "edit_news_announcement": "Edit News/Announcement", "start": "Start", "whats_new_on_smartpanel": "What's new on SmartPanel", "invalid_news_type": "Invalid news type!", "start_field_is_required": "Start field is required", "description_field_is_required": "Description field is required", "expiry_field_is_required": "Expiry field is required", "modules": "<PERSON><PERSON><PERSON>", "purchased": "Purchased", "buy_now": "Buy Now", "upgrade_version": "Upgrade to version", "clear_all": "Clear all", "role": "Role", "ip_address": "IP_Address", "date_time": "DateTime", "check_in": "Check in", "check_out": "Check out", "banned_by": "Banned By", "newsletter": "Newsletter", "fill_in_the_ridiculously_small_form_below_to_receive_our_ridiculously_cool_newsletter": "Fill in the ridiculously small form below to receive our ridiculously cool newsletter!                                        ", "subscribe_now": "Subscribe now", "you_subscribed_successfully_to_our_newsletter_thank_you_for_your_subsrciption": "You subscribed successfully to our newsletter. Thank you for your subsrciption                                        ", "an_error_occurred_while_subscribing_please_try_again": "An error occurred while subscribing. Please try again.", "a_subscriber_for_the_specified_email_address_already_exists_try_another_email_address": "A subscriber for the specified email address already exists. Try another email address                                        ", "cookie_policy_page": "<PERSON>ie <PERSON> Page", "payments_bonuses": "Payments Bonuses", "method": "Method", "bonus_percentage": "Bonus Percentage (%)", "bonus_from": "Bonus From", "add_bonus": "Add bonus", "no_payment_option": "No Payment Option", "payments_methods": "Payments Methods", "new_users": "New users", "method_name": "Method name", "maximal_payment": "Maximal payment", "allowed": "Allowed", "not_allowed": "Not Allowed", "take_fee_from_user": "Take fee from user"}