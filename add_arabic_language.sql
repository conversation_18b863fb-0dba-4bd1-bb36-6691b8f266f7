-- إضافة اللغة العربية إلى قاعدة البيانات
-- تشغيل هذا الملف لإضافة اللغة العربية وتعيينها كلغة افتراضية

-- أولاً، إزالة اللغة الافتراضية الحالية
UPDATE `general_lang_list` SET `is_default` = 0 WHERE `is_default` = 1;

-- إضافة اللغة العربية كلغة افتراضية
INSERT INTO `general_lang_list` (`ids`, `code`, `country_code`, `is_default`, `status`, `created`) VALUES
('ar_' + SUBSTRING(MD5(RAND()), 1, 28), 'ar', 'SA', 1, 1, NOW());

-- أو إذا كنت تريد إبقاء الإنجليزية كافتراضية وإضافة العربية كخيار إضافي:
-- INSERT INTO `general_lang_list` (`ids`, `code`, `country_code`, `is_default`, `status`, `created`) VALUES
-- ('ar_' + SUBSTRING(MD5(RAND()), 1, 28), 'ar', 'SA', 0, 1, NOW());
