{"name": "markbaker/matrix", "type": "library", "description": "PHP Class for working with matrices", "keywords": ["matrix", "vector", "mathematics"], "homepage": "https://github.com/MarkBaker/PHPMatrix", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2"}, "require-dev": {"phpunit/phpunit": "^8.4@dev", "squizlabs/php_codesniffer": "^3.0@dev", "phpmd/phpmd": "dev-master", "infection/infection": "0.13.x-dev", "phpstan/phpstan": "^0.12.0@dev", "sebastian/phpcpd": "^4.1", "phploc/phploc": "^5.0@dev", "phpcompatibility/php-compatibility": "dev-master", "dealerdirect/phpcodesniffer-composer-installer": "dev-master"}, "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "autoload-dev": {"psr-4": {"Matrix\\Test\\": "unitTests/classes/src/"}, "files": ["unitTests/classes/src/functions/adjointTest.php", "unitTests/classes/src/functions/antidiagonalTest.php", "unitTests/classes/src/functions/cofactorsTest.php", "unitTests/classes/src/functions/determinantTest.php", "unitTests/classes/src/functions/diagonalTest.php", "unitTests/classes/src/functions/identityTest.php", "unitTests/classes/src/functions/inverseTest.php", "unitTests/classes/src/functions/minorsTest.php", "unitTests/classes/src/functions/traceTest.php", "unitTests/classes/src/functions/transposeTest.php", "unitTests/classes/src/operations/addTest.php", "unitTests/classes/src/operations/directsumTest.php", "unitTests/classes/src/operations/subtractTest.php", "unitTests/classes/src/operations/multiplyTest.php", "unitTests/classes/src/operations/dividebyTest.php", "unitTests/classes/src/operations/divideintoTest.php"]}, "scripts": {"style": "phpcs --report-width=200 --report=summary,full -n", "test": "phpunit -c phpunit.xml.dist", "mess": "phpmd classes/src/ xml codesize,unusedcode,design,naming -n", "lines": "phploc classes/src/ -n", "cpd": "phpcpd classes/src/ -n", "versions": "phpcs --report-width=200 --report=summary,full classes/src/ --standard=PHPCompatibility --runtime-set testVersion 5.6- -n", "infection": "infection --min-msi=70 --min-covered-msi=70 --log-verbosity=all", "phpstan": "phpstan analyse classes/src/ -c phpstan.neon --level=7 --no-progress -vvv --memory-limit=1024M", "coverage": "phpunit -c phpunit.xml.dist --coverage-text --coverage-html ./build/coverage"}, "minimum-stability": "dev"}