# تعليمات تفعيل اللغة العربية في الموقع

## ملخص ما تم إنجازه:

تم إنشاء ترجمة كاملة للموقع إلى اللغة العربية مع تقسيم الملفات حسب الوظائف:

### 1. ملفات الترجمة المنشأة:

- **`app/language/data/ar_lang.php`** - الملف الرئيسي للترجمة العربية (شامل)
- **`app/language/data/ar_homepage.php`** - ترجمة الصفحة الرئيسية
- **`app/language/data/ar_auth.php`** - ترجمة تسجيل الدخول والمصادقة
- **`app/language/data/ar_orders.php`** - ترجمة الطلبات والخدمات
- **`app/language/data/ar_payments.php`** - ترجمة المدفوعات والمعاملات
- **`app/language/data/ar_admin.php`** - ترجمة الإدارة والإعدادات
- **`app/language/data/ar_tickets.php`** - ترجمة التذاكر والدعم
- **`app/language/data/ar_api.php`** - ترجمة واجهة برمجة التطبيقات
- **`app/language/data/ar_common.php`** - ترجمة العناصر العامة والأزرار
- **`app/language/data/ar_email.php`** - ترجمة البريد الإلكتروني والإشعارات

### 2. ملفات الدعم المنشأة:

- **`assets/css/arabic-rtl.css`** - ملف CSS لدعم اللغة العربية (RTL)
- **`add_arabic_language.sql`** - ملف SQL لإضافة اللغة العربية إلى قاعدة البيانات

## خطوات التفعيل:

### الخطوة 1: إضافة اللغة العربية إلى قاعدة البيانات

1. افتح phpMyAdmin أو أي أداة إدارة قاعدة بيانات
2. اختر قاعدة البيانات الخاصة بالموقع
3. شغل الملف `add_arabic_language.sql` لإضافة اللغة العربية

```sql
-- إضافة اللغة العربية كلغة افتراضية
UPDATE `general_lang_list` SET `is_default` = 0 WHERE `is_default` = 1;
INSERT INTO `general_lang_list` (`ids`, `code`, `country_code`, `is_default`, `status`, `created`) VALUES
(CONCAT('ar_', SUBSTRING(MD5(RAND()), 1, 28)), 'ar', 'SA', 1, 1, NOW());
```

### الخطوة 2: إضافة ملف CSS للغة العربية

1. تأكد من وجود ملف `assets/css/arabic-rtl.css`
2. أضف الكود التالي إلى ملف الرأس الرئيسي للموقع:

```html
<link rel="stylesheet" href="<?=base_url('assets/css/arabic-rtl.css')?>" id="rtl-css">
```

### الخطوة 3: تحديث ملفات القوالب (اختياري)

إذا كنت تريد تطبيق RTL تلقائياً عند اختيار اللغة العربية، أضف الكود التالي:

```php
<?php
$current_lang = session('langCurrent');
$is_rtl = ($current_lang && $current_lang->code == 'ar') ? 'rtl' : 'ltr';
?>
<html dir="<?=$is_rtl?>" lang="<?=$current_lang->code ?? 'en'?>">
```

### الخطوة 4: تفعيل اللغة العربية من لوحة الإدارة

1. ادخل إلى لوحة الإدارة
2. اذهب إلى **الإعدادات** > **اللغة** (Settings > Language)
3. ستجد اللغة العربية مضافة تلقائياً
4. يمكنك تعديل الترجمات من خلال محرر الترجمة

## الميزات المضافة:

### 1. ترجمة شاملة:
- جميع عناصر الواجهة
- رسائل النظام والأخطاء
- القوائم والأزرار
- النماذج والحقول
- الإشعارات والتنبيهات

### 2. دعم RTL:
- اتجاه النص من اليمين إلى اليسار
- ترتيب العناصر المناسب للغة العربية
- تخطيط متجاوب مع الجوال

### 3. تنظيم الملفات:
- ملفات منفصلة لكل قسم
- سهولة الصيانة والتحديث
- إمكانية التخصيص حسب الحاجة

## استكشاف الأخطاء:

### إذا لم تظهر اللغة العربية:
1. تأكد من تشغيل ملف SQL
2. تحقق من وجود ملفات الترجمة في المجلد الصحيح
3. امسح الكاش إذا كان موجوداً

### إذا لم يعمل RTL:
1. تأكد من إضافة ملف CSS
2. تحقق من إضافة الكود في ملف الرأس
3. تأكد من إعداد `dir="rtl"` في عنصر HTML

### إذا كانت بعض النصوص لا تزال بالإنجليزية:
1. ابحث عن النص في ملفات الترجمة
2. أضف الترجمة المفقودة
3. تأكد من استخدام دالة `lang()` في الكود

## ملاحظات مهمة:

1. **النسخ الاحتياطي**: تأكد من عمل نسخة احتياطية قبل التطبيق
2. **الاختبار**: اختبر جميع الصفحات بعد التفعيل
3. **التحديثات**: عند تحديث النظام، تأكد من الحفاظ على ملفات الترجمة
4. **التخصيص**: يمكنك تعديل الترجمات حسب احتياجاتك

## الدعم:

إذا واجهت أي مشاكل في التفعيل، تأكد من:
- صحة مسارات الملفات
- صلاحيات الكتابة على المجلدات
- إعدادات قاعدة البيانات
- إعدادات الخادم

---

**تم إنشاء هذه الترجمة بواسطة Augment Agent**
**جميع الملفات جاهزة للاستخدام المباشر**
