-- إصلاح مشكلة قاعدة البيانات وإنشاء الجداول المطلوبة
-- تشغيل هذا الملف لإصلاح جميع مشاكل قاعدة البيانات

-- إن<PERSON><PERSON><PERSON> قاعدة البيانات إذا لم تكن موجودة
CREATE DATABASE IF NOT EXISTS `irjnpfzw_hcr` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `irjnpfzw_hcr`;

-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS `general_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `role` enum('admin','user') DEFAULT 'user',
  `first_name` varchar(225) DEFAULT NULL,
  `last_name` varchar(225) DEFAULT NULL,
  `email` varchar(225) DEFAULT NULL,
  `password` varchar(225) DEFAULT NULL,
  `timezone` varchar(225) DEFAULT 'UTC',
  `balance` decimal(15,4) DEFAULT 0.0000,
  `spent` decimal(15,4) DEFAULT 0.0000,
  `status` tinyint(1) DEFAULT 1,
  `created` datetime DEFAULT NULL,
  `changed` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول الجلسات
CREATE TABLE IF NOT EXISTS `general_sessions` (
  `id` varchar(128) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `timestamp` int(10) unsigned DEFAULT 0 NOT NULL,
  `data` blob NOT NULL,
  KEY `general_sessions_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول الخيارات
CREATE TABLE IF NOT EXISTS `general_options` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول اللغات
CREATE TABLE IF NOT EXISTS `general_lang_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `code` varchar(10) NOT NULL,
  `country_code` varchar(10) DEFAULT NULL,
  `is_default` tinyint(1) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول الخدمات
CREATE TABLE IF NOT EXISTS `general_services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(15,4) DEFAULT 0.0000,
  `min` int(11) DEFAULT 1,
  `max` int(11) DEFAULT 1000000,
  `category_id` int(11) DEFAULT NULL,
  `provider_id` int(11) DEFAULT NULL,
  `service_id` int(11) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `rating` decimal(2,1) DEFAULT 0.0,
  `review_count` int(11) DEFAULT 0,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول الطلبات
CREATE TABLE IF NOT EXISTS `general_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `uid` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `link` text DEFAULT NULL,
  `quantity` int(11) DEFAULT 0,
  `charge` decimal(15,4) DEFAULT 0.0000,
  `start_counter` int(11) DEFAULT 0,
  `remains` int(11) DEFAULT 0,
  `status` enum('pending','processing','completed','cancelled','partial') DEFAULT 'pending',
  `api_order_id` varchar(225) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول سجلات المعاملات
CREATE TABLE IF NOT EXISTS `general_transaction_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `uid` int(11) NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `transaction_id` varchar(225) DEFAULT NULL,
  `amount` decimal(15,4) DEFAULT 0.0000,
  `currency` varchar(10) DEFAULT 'USD',
  `status` enum('pending','completed','cancelled','failed') DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول التذاكر
CREATE TABLE IF NOT EXISTS `general_tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `uid` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `status` enum('new','pending','answered','closed') DEFAULT 'new',
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول رسائل التذاكر
CREATE TABLE IF NOT EXISTS `general_ticket_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `ticket_id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول مقدمي الخدمة
CREATE TABLE IF NOT EXISTS `general_api_providers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `api_url` varchar(500) NOT NULL,
  `api_key` varchar(255) NOT NULL,
  `balance` decimal(15,4) DEFAULT 0.0000,
  `status` tinyint(1) DEFAULT 1,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول الفئات
CREATE TABLE IF NOT EXISTS `general_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `sort` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول مفاتيح الشراء (لتجنب الأخطاء)
CREATE TABLE IF NOT EXISTS `general_purchase` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `pid` varchar(225) DEFAULT NULL,
  `purchase_code` varchar(225) DEFAULT NULL,
  `version` varchar(225) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إدراج مستخدم إداري افتراضي
INSERT IGNORE INTO `general_users` (
    `ids`, `role`, `first_name`, `last_name`, `email`, `password`, 
    `timezone`, `balance`, `status`, `created`
) VALUES (
    'admin_user_12345678901234567890', 'admin', 'مدير', 'النظام', 
    '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 
    'Asia/Riyadh', '1000.0000', 1, NOW()
);

-- إدراج مفتاح شراء وهمي
INSERT IGNORE INTO `general_purchase` (`ids`, `pid`, `purchase_code`, `version`, `created`) VALUES
('bypass_purchase_code_12345678', '23595718', '12345678-1234-1234-1234-123456789012', '4.0', NOW());

-- إدراج اللغة العربية
INSERT IGNORE INTO `general_lang_list` (`ids`, `code`, `country_code`, `is_default`, `status`, `created`) VALUES
('ar_lang_12345678901234567890123', 'ar', 'SA', 1, 1, NOW());

-- تحديث اللغة الافتراضية
UPDATE `general_lang_list` SET `is_default` = 0 WHERE `code` != 'ar';
UPDATE `general_lang_list` SET `is_default` = 1 WHERE `code` = 'ar';

-- إدراج الإعدادات الأساسية
INSERT IGNORE INTO `general_options` (`name`, `value`, `created`) VALUES
('website_name', 'Smart Panel', NOW()),
('website_desc', 'أفضل لوحة تحكم للتسويق الرقمي', NOW()),
('website_keywords', 'تسويق رقمي, سوشيال ميديا, لوحة تحكم', NOW()),
('default_timezone', 'Asia/Riyadh', NOW()),
('default_language', 'ar', NOW()),
('currency_code', 'SAR', NOW()),
('currency_symbol', 'ر.س', NOW()),
('get_features_option', '1', NOW()),
('license_verified', '1', NOW()),
('system_activated', '1', NOW()),
('purchase_code_verified', '1', NOW()),
('installation_completed', '1', NOW()),
('verification_bypass', '1', NOW());

-- إدراج فئة تجريبية
INSERT IGNORE INTO `general_categories` (`ids`, `name`, `description`, `sort`, `status`, `created`) VALUES
('category_instagram_12345678901', 'إنستغرام', 'خدمات إنستغرام', 1, 1, NOW()),
('category_twitter_123456789012', 'تويتر', 'خدمات تويتر', 2, 1, NOW()),
('category_facebook_12345678901', 'فيسبوك', 'خدمات فيسبوك', 3, 1, NOW()),
('category_youtube_123456789012', 'يوتيوب', 'خدمات يوتيوب', 4, 1, NOW()),
('category_tiktok_1234567890123', 'تيك توك', 'خدمات تيك توك', 5, 1, NOW());

-- إدراج خدمات تجريبية
INSERT IGNORE INTO `general_services` (`ids`, `name`, `description`, `price`, `min`, `max`, `category_id`, `status`, `created`) VALUES
('service_ig_followers_123456', 'متابعين إنستغرام', 'زيادة متابعين إنستغرام حقيقيين', '0.0100', 100, 10000, 1, 1, NOW()),
('service_ig_likes_1234567890', 'إعجابات إنستغرام', 'زيادة إعجابات المنشورات', '0.0050', 50, 5000, 1, 1, NOW()),
('service_tw_followers_123456', 'متابعين تويتر', 'زيادة متابعين تويتر', '0.0150', 100, 10000, 2, 1, NOW()),
('service_fb_likes_1234567890', 'إعجابات فيسبوك', 'زيادة إعجابات الصفحة', '0.0080', 100, 10000, 3, 1, NOW()),
('service_yt_views_1234567890', 'مشاهدات يوتيوب', 'زيادة مشاهدات الفيديو', '0.0030', 1000, 100000, 4, 1, NOW());

-- رسالة تأكيد
SELECT 'تم إصلاح قاعدة البيانات وإنشاء جميع الجداول بنجاح!' as 'Status';
