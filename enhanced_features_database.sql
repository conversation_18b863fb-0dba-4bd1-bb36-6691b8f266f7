-- إنشاء الجداول للميزات المحسنة
-- تشغيل هذا الملف لإضافة جميع الميزات الجديدة

-- جدول الإشعارات
CREATE TABLE IF NOT EXISTS `general_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error','reward') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الكوبونات
CREATE TABLE IF NOT EXISTS `general_coupons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `code` varchar(50) NOT NULL UNIQUE,
  `description` text DEFAULT NULL,
  `discount_type` enum('percentage','fixed') DEFAULT 'percentage',
  `discount_value` decimal(10,2) NOT NULL,
  `max_discount` decimal(10,2) DEFAULT 0,
  `min_order_amount` decimal(10,2) DEFAULT 0,
  `usage_limit` int(11) DEFAULT 0,
  `user_limit` int(11) DEFAULT 1,
  `user_id` int(11) DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 1,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `code` (`code`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول استخدام الكوبونات
CREATE TABLE IF NOT EXISTS `general_coupon_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `coupon_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `discount_amount` decimal(10,2) NOT NULL,
  `used_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `coupon_id` (`coupon_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول التقييمات
CREATE TABLE IF NOT EXISTS `general_reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `rating` tinyint(1) NOT NULL CHECK (rating >= 1 AND rating <= 5),
  `comment` text DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `admin_reply` text DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `service_id` (`service_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول إعجابات التقييمات
CREATE TABLE IF NOT EXISTS `general_review_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `review_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `review_user` (`review_id`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول محادثات الدردشة
CREATE TABLE IF NOT EXISTS `general_chat_conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `subject` varchar(255) NOT NULL,
  `status` enum('open','closed','pending') DEFAULT 'open',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `created` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول رسائل الدردشة
CREATE TABLE IF NOT EXISTS `general_chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `conversation_id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `message_type` enum('text','image','file') DEFAULT 'text',
  `attachment` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `conversation_id` (`conversation_id`),
  KEY `sender_id` (`sender_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول نقاط الولاء
CREATE TABLE IF NOT EXISTS `general_loyalty_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `points` int(11) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول مكافآت الولاء
CREATE TABLE IF NOT EXISTS `general_loyalty_rewards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `reward_type` enum('balance','discount','free_service','special_offer') NOT NULL,
  `reward_value` decimal(10,2) NOT NULL,
  `points_required` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول استبدال المكافآت
CREATE TABLE IF NOT EXISTS `general_loyalty_redemptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `reward_id` int(11) NOT NULL,
  `points_used` int(11) NOT NULL,
  `status` enum('pending','completed','cancelled') DEFAULT 'pending',
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `reward_id` (`reward_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- جدول الخدمات المجانية
CREATE TABLE IF NOT EXISTS `general_free_services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ids` varchar(225) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `status` enum('available','used','expired') DEFAULT 'available',
  `expires_at` datetime DEFAULT NULL,
  `used_at` datetime DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `service_id` (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- إضافة عمود التقييم لجدول الخدمات
ALTER TABLE `general_services` 
ADD COLUMN `rating` decimal(2,1) DEFAULT 0.0,
ADD COLUMN `review_count` int(11) DEFAULT 0;

-- إدراج بعض الكوبونات التجريبية
INSERT INTO `general_coupons` (`ids`, `code`, `description`, `discount_type`, `discount_value`, `min_order_amount`, `usage_limit`, `start_date`, `end_date`, `status`, `created`) VALUES
(CONCAT('coupon_', SUBSTRING(MD5(RAND()), 1, 24)), 'WELCOME10', 'خصم ترحيبي للعملاء الجدد', 'percentage', 10.00, 50.00, 100, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 1, NOW()),
(CONCAT('coupon_', SUBSTRING(MD5(RAND()), 1, 24)), 'SAVE20', 'خصم 20% على الطلبات الكبيرة', 'percentage', 20.00, 200.00, 50, NOW(), DATE_ADD(NOW(), INTERVAL 60 DAY), 1, NOW()),
(CONCAT('coupon_', SUBSTRING(MD5(RAND()), 1, 24)), 'FIXED50', 'خصم ثابت 50 ريال', 'fixed', 50.00, 300.00, 25, NOW(), DATE_ADD(NOW(), INTERVAL 45 DAY), 1, NOW());

-- إدراج مكافآت الولاء التجريبية
INSERT INTO `general_loyalty_rewards` (`ids`, `name`, `description`, `reward_type`, `reward_value`, `points_required`, `status`, `created`) VALUES
(CONCAT('reward_', SUBSTRING(MD5(RAND()), 1, 24)), 'رصيد مجاني 10 ريال', 'احصل على 10 ريال رصيد مجاني', 'balance', 10.00, 100, 1, NOW()),
(CONCAT('reward_', SUBSTRING(MD5(RAND()), 1, 24)), 'خصم 15%', 'كوبون خصم 15% على طلبك القادم', 'discount', 15.00, 200, 1, NOW()),
(CONCAT('reward_', SUBSTRING(MD5(RAND()), 1, 24)), 'رصيد مجاني 25 ريال', 'احصل على 25 ريال رصيد مجاني', 'balance', 25.00, 300, 1, NOW()),
(CONCAT('reward_', SUBSTRING(MD5(RAND()), 1, 24)), 'خصم 25%', 'كوبون خصم 25% على طلبك القادم', 'discount', 25.00, 500, 1, NOW());

-- إضافة إعدادات نظام النقاط
INSERT INTO `general_options` (`name`, `value`, `created`) VALUES
('loyalty_points_per_dollar', '10', NOW()),
('loyalty_points_signup_bonus', '50', NOW()),
('loyalty_points_review_bonus', '25', NOW()),
('loyalty_points_referral_bonus', '100', NOW())
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`);

-- رسالة تأكيد
SELECT 'تم إنشاء جميع الجداول والميزات المحسنة بنجاح!' as 'Status';
