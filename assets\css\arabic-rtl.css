/* ملف CSS لدعم اللغة العربية (RTL) */

/* إعدادات عامة للغة العربية */
html[lang="ar"], 
html[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

body[dir="rtl"] {
    direction: rtl;
    text-align: right;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
}

/* إعدادات النصوص */
.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

/* إعدادات القوائم */
.navbar-nav.rtl {
    flex-direction: row-reverse;
}

.nav-item.rtl {
    margin-left: 0;
    margin-right: 1rem;
}

/* إعدادات الأزرار */
.btn.rtl {
    text-align: center;
}

.btn-group.rtl {
    flex-direction: row-reverse;
}

/* إعدادات النماذج */
.form-group.rtl label {
    text-align: right;
}

.form-control.rtl {
    text-align: right;
    direction: rtl;
}

.input-group.rtl {
    flex-direction: row-reverse;
}

.input-group-prepend.rtl {
    margin-left: -1px;
    margin-right: 0;
}

.input-group-append.rtl {
    margin-right: -1px;
    margin-left: 0;
}

/* إعدادات الجداول */
.table.rtl {
    direction: rtl;
}

.table.rtl th,
.table.rtl td {
    text-align: right;
}

.table.rtl th:first-child,
.table.rtl td:first-child {
    border-right: 0;
}

.table.rtl th:last-child,
.table.rtl td:last-child {
    border-left: 0;
}

/* إعدادات البطاقات */
.card.rtl {
    direction: rtl;
}

.card-header.rtl {
    text-align: right;
}

.card-body.rtl {
    text-align: right;
}

/* إعدادات القوائم المنسدلة */
.dropdown-menu.rtl {
    right: 0;
    left: auto;
    text-align: right;
}

.dropdown-item.rtl {
    text-align: right;
}

/* إعدادات الشريط الجانبي */
.sidebar.rtl {
    right: 0;
    left: auto;
}

.sidebar.rtl .nav-link {
    text-align: right;
}

/* إعدادات المحتوى الرئيسي */
.main-content.rtl {
    margin-right: 250px;
    margin-left: 0;
}

/* إعدادات الأيقونات */
.icon-left.rtl {
    margin-left: 0.5rem;
    margin-right: 0;
}

.icon-right.rtl {
    margin-right: 0.5rem;
    margin-left: 0;
}

/* إعدادات التنبيهات */
.alert.rtl {
    text-align: right;
}

.alert-dismissible.rtl .close {
    right: auto;
    left: 0;
}

/* إعدادات النوافذ المنبثقة */
.modal.rtl .modal-dialog {
    direction: rtl;
}

.modal.rtl .modal-header {
    text-align: right;
}

.modal.rtl .modal-body {
    text-align: right;
}

.modal.rtl .modal-footer {
    justify-content: flex-start;
}

/* إعدادات التبويبات */
.nav-tabs.rtl {
    border-bottom: 1px solid #dee2e6;
}

.nav-tabs.rtl .nav-link {
    border: 1px solid transparent;
    border-top-right-radius: 0.25rem;
    border-top-left-radius: 0.25rem;
}

/* إعدادات الترقيم */
.pagination.rtl {
    flex-direction: row-reverse;
}

.page-item.rtl .page-link {
    text-align: center;
}

/* إعدادات شريط التقدم */
.progress.rtl {
    direction: ltr;
}

.progress-bar.rtl {
    direction: ltr;
}

/* إعدادات القوائم */
.list-group.rtl {
    direction: rtl;
}

.list-group-item.rtl {
    text-align: right;
}

/* إعدادات الخبز المفتت */
.breadcrumb.rtl {
    flex-direction: row-reverse;
}

.breadcrumb-item.rtl + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* إعدادات الشبكة */
.row.rtl {
    margin-right: -15px;
    margin-left: -15px;
}

.col.rtl,
[class*="col-"].rtl {
    padding-right: 15px;
    padding-left: 15px;
}

/* إعدادات النصوص */
.text-right.rtl {
    text-align: right !important;
}

.text-left.rtl {
    text-align: left !important;
}

.float-right.rtl {
    float: right !important;
}

.float-left.rtl {
    float: left !important;
}

/* إعدادات الهوامش والحشو */
.mr-auto.rtl {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.ml-auto.rtl {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.pr-0.rtl { padding-right: 0 !important; }
.pl-0.rtl { padding-left: 0 !important; }
.pr-1.rtl { padding-right: 0.25rem !important; }
.pl-1.rtl { padding-left: 0.25rem !important; }
.pr-2.rtl { padding-right: 0.5rem !important; }
.pl-2.rtl { padding-left: 0.5rem !important; }
.pr-3.rtl { padding-right: 1rem !important; }
.pl-3.rtl { padding-left: 1rem !important; }
.pr-4.rtl { padding-right: 1.5rem !important; }
.pl-4.rtl { padding-left: 1.5rem !important; }
.pr-5.rtl { padding-right: 3rem !important; }
.pl-5.rtl { padding-left: 3rem !important; }

.mr-0.rtl { margin-right: 0 !important; }
.ml-0.rtl { margin-left: 0 !important; }
.mr-1.rtl { margin-right: 0.25rem !important; }
.ml-1.rtl { margin-left: 0.25rem !important; }
.mr-2.rtl { margin-right: 0.5rem !important; }
.ml-2.rtl { margin-left: 0.5rem !important; }
.mr-3.rtl { margin-right: 1rem !important; }
.ml-3.rtl { margin-left: 1rem !important; }
.mr-4.rtl { margin-right: 1.5rem !important; }
.ml-4.rtl { margin-left: 1.5rem !important; }
.mr-5.rtl { margin-right: 3rem !important; }
.ml-5.rtl { margin-left: 3rem !important; }

/* إعدادات خاصة بالموقع */
.header-menu.rtl {
    direction: rtl;
}

.footer.rtl {
    direction: rtl;
    text-align: right;
}

.service-card.rtl {
    text-align: right;
}

.order-form.rtl {
    direction: rtl;
}

.order-form.rtl input,
.order-form.rtl select,
.order-form.rtl textarea {
    text-align: right;
}

/* إعدادات الجوال */
@media (max-width: 768px) {
    .main-content.rtl {
        margin-right: 0;
        margin-left: 0;
    }
    
    .sidebar.rtl {
        right: -250px;
    }
    
    .sidebar.rtl.show {
        right: 0;
    }
}

/* إعدادات الطباعة */
@media print {
    body.rtl {
        direction: rtl;
        text-align: right;
    }
}
