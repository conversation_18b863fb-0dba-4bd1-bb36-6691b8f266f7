# 🔧 إصلاح خطأ HTTP ERROR 500

## 📋 **تشخيص المشكلة:**

خطأ **HTTP ERROR 500** يحدث عادة بسبب:
1. **مشكلة في إعدادات قاعدة البيانات**
2. **عدم وجود الجداول المطلوبة**
3. **خطأ في بيانات الاتصال**
4. **مشكلة في صلاحيات الملفات**

## 🛠️ **الحلول المرتبة حسب الأولوية:**

### **الحل الأول: إصلاح إعدادات قاعدة البيانات**

#### **الخطوة 1: التحقق من بيانات الاتصال**
```
اسم قاعدة البيانات: irjnpfzw_hcr
اسم المستخدم: irjnpfzw_hcr  
كلمة المرور: irjnpfzw_hcr
```

#### **الخطوة 2: تشغيل ملف اختبار الاتصال**
1. ارفع ملف `test_database_connection.php` إلى المجلد الرئيسي
2. افتح الرابط: `http://tikt0k.co/test_database_connection.php`
3. تحقق من النتائج

#### **الخطوة 3: إصلاح قاعدة البيانات**
1. ادخل إلى **cPanel** > **phpMyAdmin**
2. اختر قاعدة البيانات `irjnpfzw_hcr`
3. اذهب إلى تبويب **SQL**
4. انسخ محتوى ملف `fix_database_connection.sql` والصقه
5. اضغط **تنفيذ**

### **الحل الثاني: التحقق من إعدادات الملفات**

#### **تحديث ملف app/config.php:**
```php
<?php
//Config Database
define('DB_HOST', 'localhost');
define('DB_USER', 'irjnpfzw_hcr');
define('DB_PASS', 'irjnpfzw_hcr');
define('DB_NAME', 'irjnpfzw_hcr');
define('TIMEZONE', 'Asia/Riyadh');
define('ENCRYPTION_KEY', 'c5fbb0694cb9c7e6dda5926478149672');
```

### **الحل الثالث: إصلاح صلاحيات الملفات**

#### **عبر cPanel File Manager:**
1. اذهب إلى **File Manager**
2. حدد المجلدات التالية:
   - `app/cache/` → صلاحية **755**
   - `app/logs/` → صلاحية **755**
   - `assets/` → صلاحية **755**
3. حدد الملفات PHP → صلاحية **644**

#### **عبر SSH (إذا متاح):**
```bash
chmod -R 755 app/cache/
chmod -R 755 app/logs/
chmod -R 755 assets/
chmod 644 app/config.php
chmod 644 index.php
```

### **الحل الرابع: إنشاء قاعدة البيانات من الصفر**

#### **إذا لم تكن قاعدة البيانات موجودة:**
1. ادخل إلى **cPanel** > **MySQL Databases**
2. أنشئ قاعدة بيانات جديدة: `irjnpfzw_hcr`
3. أنشئ مستخدم جديد: `irjnpfzw_hcr`
4. اربط المستخدم بقاعدة البيانات مع **جميع الصلاحيات**
5. شغل ملف `fix_database_connection.sql`

## 🔍 **التحقق من الإصلاح:**

### **الخطوة 1: اختبار الاتصال**
```
افتح: http://tikt0k.co/test_database_connection.php
```

### **الخطوة 2: اختبار الموقع**
```
افتح: http://tikt0k.co/
```

### **الخطوة 3: تسجيل دخول الإدارة**
```
الرابط: http://tikt0k.co/admin
البريد: <EMAIL>
كلمة المرور: 123456
```

## 🚨 **إذا استمر الخطأ:**

### **تفعيل سجلات الأخطاء:**
1. أضف هذا الكود في بداية ملف `index.php`:
```php
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');
```

2. افتح الموقع وتحقق من ملف `error.log`

### **التحقق من سجلات الخادم:**
1. ادخل إلى **cPanel** > **Error Logs**
2. تحقق من آخر الأخطاء
3. ابحث عن أخطاء PHP أو MySQL

## 📞 **الدعم الإضافي:**

### **معلومات مفيدة للدعم:**
- **نوع الخادم:** cPanel/Linux
- **إصدار PHP:** (تحقق من cPanel)
- **إصدار MySQL:** (تحقق من phpMyAdmin)
- **المجال:** tikt0k.co

### **ملفات مهمة للفحص:**
- `app/config.php` - إعدادات قاعدة البيانات
- `app/config/database.php` - إعدادات CodeIgniter
- `.htaccess` - إعدادات الخادم
- `error.log` - سجل الأخطاء

## ✅ **قائمة التحقق النهائية:**

- [ ] تم تحديث إعدادات قاعدة البيانات
- [ ] تم إنشاء/إصلاح قاعدة البيانات
- [ ] تم تشغيل ملف SQL الإصلاحي
- [ ] تم التحقق من صلاحيات الملفات
- [ ] تم اختبار الاتصال بقاعدة البيانات
- [ ] تم اختبار تسجيل دخول الإدارة
- [ ] تم حذف ملفات الاختبار

## 🎯 **النتيجة المتوقعة:**

بعد تطبيق هذه الحلول:
- ✅ الموقع يعمل بدون أخطاء
- ✅ يمكن الوصول للصفحة الرئيسية
- ✅ يمكن تسجيل دخول الإدارة
- ✅ اللغة العربية تعمل بشكل صحيح
- ✅ جميع الميزات متاحة

---

**💡 نصيحة:** احتفظ بنسخة احتياطية من الملفات قبل أي تعديل!

**🔒 أمان:** احذف ملف `test_database_connection.php` بعد الانتهاء من الإصلاح!
