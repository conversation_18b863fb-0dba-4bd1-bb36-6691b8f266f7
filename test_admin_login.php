<?php
/**
 * ملف اختبار تسجيل دخول الإدارة
 * تشغيل هذا الملف للتحقق من إمكانية تسجيل دخول الإدارة
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'irjnpfzw_hcr';
$db_pass = 'irjnpfzw_hcr';
$db_name = 'irjnpfzw_hcr';

echo "<h2>🔐 اختبار تسجيل دخول الإدارة</h2>";
echo "<hr>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ <strong>نجح الاتصال بقاعدة البيانات!</strong><br><br>";
    
    // التحقق من وجود المستخدم الإداري
    $stmt = $pdo->prepare("SELECT * FROM general_users WHERE role = 'admin' AND email = '<EMAIL>'");
    $stmt->execute();
    $admin_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin_user) {
        echo "✅ <strong>المستخدم الإداري موجود!</strong><br>";
        echo "📧 البريد الإلكتروني: <strong>" . $admin_user['email'] . "</strong><br>";
        echo "👤 الاسم: <strong>" . $admin_user['first_name'] . " " . $admin_user['last_name'] . "</strong><br>";
        echo "🔑 كلمة المرور المشفرة: <strong>" . substr($admin_user['password'], 0, 20) . "...</strong><br>";
        echo "📊 الحالة: <strong>" . ($admin_user['status'] ? 'نشط' : 'غير نشط') . "</strong><br>";
        echo "🌍 المنطقة الزمنية: <strong>" . $admin_user['timezone'] . "</strong><br>";
        echo "💰 الرصيد: <strong>" . $admin_user['balance'] . "</strong><br><br>";
        
        // التحقق من كلمة المرور
        $test_password = '123456';
        $hashed_password = md5($test_password);
        
        if ($admin_user['password'] === $hashed_password) {
            echo "✅ <strong>كلمة المرور صحيحة!</strong><br>";
        } else {
            echo "❌ <strong>كلمة المرور غير صحيحة!</strong><br>";
            echo "💡 <strong>الحل:</strong> تشغيل ملف fix_admin_login.sql<br>";
        }
        
    } else {
        echo "❌ <strong>المستخدم الإداري غير موجود!</strong><br>";
        echo "💡 <strong>الحل:</strong> تشغيل ملف fix_admin_login.sql<br>";
    }
    
    echo "<br>";
    
    // التحقق من جدول الجلسات
    $stmt = $pdo->query("SHOW TABLES LIKE 'general_sessions'");
    if ($stmt->rowCount() > 0) {
        echo "✅ <strong>جدول الجلسات موجود</strong><br>";
        
        // عدد الجلسات النشطة
        $stmt = $pdo->query("SELECT COUNT(*) FROM general_sessions WHERE timestamp > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR))");
        $active_sessions = $stmt->fetchColumn();
        echo "📊 الجلسات النشطة: <strong>$active_sessions</strong><br>";
        
    } else {
        echo "❌ <strong>جدول الجلسات غير موجود!</strong><br>";
    }
    
    echo "<br>";
    
    // التحقق من الإعدادات
    $stmt = $pdo->query("SELECT COUNT(*) FROM general_options WHERE name = 'verification_bypass' AND value = '1'");
    $bypass_enabled = $stmt->fetchColumn();
    
    if ($bypass_enabled > 0) {
        echo "✅ <strong>تم تجاوز التحقق من مفتاح الشراء</strong><br>";
    } else {
        echo "❌ <strong>لم يتم تجاوز التحقق من مفتاح الشراء</strong><br>";
    }
    
    // التحقق من اللغة العربية
    $stmt = $pdo->query("SELECT COUNT(*) FROM general_lang_list WHERE code = 'ar' AND is_default = 1");
    $arabic_default = $stmt->fetchColumn();
    
    if ($arabic_default > 0) {
        echo "✅ <strong>اللغة العربية هي الافتراضية</strong><br>";
    } else {
        echo "❌ <strong>اللغة العربية ليست افتراضية</strong><br>";
    }
    
} catch (PDOException $e) {
    echo "❌ <strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage() . "<br>";
}

echo "<br><hr>";
echo "<h3>🔗 روابط الاختبار:</h3>";
echo "<a href='https://tikt0k.co/admin' target='_blank' class='btn btn-primary'>🔐 صفحة تسجيل دخول الإدارة</a><br><br>";
echo "<a href='https://tikt0k.co/admin/users' target='_blank' class='btn btn-secondary'>👥 لوحة إدارة المستخدمين</a><br><br>";
echo "<a href='https://tikt0k.co/' target='_blank' class='btn btn-info'>🏠 الصفحة الرئيسية</a><br><br>";

echo "<hr>";
echo "<h3>📝 بيانات تسجيل الدخول:</h3>";
echo "<div class='login-info'>";
echo "<strong>البريد الإلكتروني:</strong> <EMAIL><br>";
echo "<strong>كلمة المرور:</strong> 123456<br>";
echo "<strong>الرابط:</strong> <a href='https://tikt0k.co/admin'>https://tikt0k.co/admin</a><br>";
echo "</div>";

echo "<br><hr>";
echo "<h3>🔧 خطوات الإصلاح (إذا لم يعمل):</h3>";
echo "<ol>";
echo "<li>تشغيل ملف <strong>fix_admin_login.sql</strong> في phpMyAdmin</li>";
echo "<li>التأكد من صحة إعدادات قاعدة البيانات</li>";
echo "<li>مسح الكاش والكوكيز من المتصفح</li>";
echo "<li>إعادة تشغيل هذا الملف للتحقق</li>";
echo "<li>حذف هذا الملف بعد التأكد من عمل النظام</li>";
echo "</ol>";

echo "<br><hr>";
echo "<p><small>تم إنشاء هذا الملف بواسطة Augment Agent</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007cba;
    padding-bottom: 5px;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

strong {
    color: #007cba;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    color: white;
    font-weight: bold;
}

.btn-primary {
    background-color: #007cba;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-info {
    background-color: #17a2b8;
}

.btn:hover {
    opacity: 0.8;
    color: white;
}

.login-info {
    background: #e9ecef;
    padding: 15px;
    border-radius: 5px;
    border-right: 4px solid #007cba;
}

ol {
    background: #fff3cd;
    padding: 15px 30px;
    border-radius: 5px;
    border-right: 4px solid #ffc107;
}

.success {
    color: #28a745;
}

.error {
    color: #dc3545;
}

.info {
    color: #17a2b8;
}
</style>
