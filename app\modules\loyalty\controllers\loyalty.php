<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class loyalty extends My_UserController {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('loyalty_model', 'main_model');
    }

    // عرض نقاط الولاء
    public function index()
    {
        $user_id = session('uid');
        $user_points = $this->main_model->get_user_points($user_id);
        $point_history = $this->main_model->get_point_history($user_id);
        $available_rewards = $this->main_model->get_available_rewards();
        $user_level = $this->main_model->get_user_level($user_points);

        $data = array(
            "user_points" => $user_points,
            "point_history" => $point_history,
            "available_rewards" => $available_rewards,
            "user_level" => $user_level
        );

        $this->template->build('index', $data);
    }

    // استبدال النقاط بمكافأة
    public function redeem_reward()
    {
        if (!$this->input->is_ajax_request()) redirect(cn());

        $reward_id = (int)post('reward_id');
        $user_id = session('uid');

        $reward = $this->main_model->get_reward($reward_id);
        
        if (!$reward) {
            ms(array(
                "status" => "error",
                "message" => "المكافأة غير موجودة"
            ));
        }

        $user_points = $this->main_model->get_user_points($user_id);

        if ($user_points < $reward->points_required) {
            ms(array(
                "status" => "error",
                "message" => "نقاطك غير كافية لاستبدال هذه المكافأة"
            ));
        }

        // استبدال المكافأة
        $redemption_id = $this->main_model->redeem_reward($user_id, $reward_id, $reward->points_required);

        // خصم النقاط
        $this->main_model->deduct_points($user_id, $reward->points_required, 'reward_redemption', $redemption_id);

        // تطبيق المكافأة حسب نوعها
        $this->apply_reward($user_id, $reward);

        ms(array(
            "status" => "success",
            "message" => "تم استبدال المكافأة بنجاح",
            "remaining_points" => $user_points - $reward->points_required
        ));
    }

    // تطبيق المكافأة
    private function apply_reward($user_id, $reward)
    {
        switch ($reward->reward_type) {
            case 'balance':
                // إضافة رصيد
                $this->add_user_balance($user_id, $reward->reward_value);
                break;
                
            case 'discount':
                // إنشاء كوبون خصم
                $this->create_discount_coupon($user_id, $reward->reward_value);
                break;
                
            case 'free_service':
                // خدمة مجانية
                $this->grant_free_service($user_id, $reward->reward_value);
                break;
        }
    }

    // إضافة رصيد للمستخدم
    private function add_user_balance($user_id, $amount)
    {
        $this->db->set('balance', 'balance + ' . $amount, FALSE)
                ->where('id', $user_id)
                ->update('general_users');

        // تسجيل المعاملة
        $transaction_data = array(
            'ids' => ids(),
            'uid' => $user_id,
            'type' => 'loyalty_reward',
            'amount' => $amount,
            'status' => 'completed',
            'created' => NOW
        );

        $this->db->insert('general_transaction_logs', $transaction_data);
    }

    // إنشاء كوبون خصم
    private function create_discount_coupon($user_id, $discount_percentage)
    {
        $coupon_code = 'LOYALTY_' . strtoupper(substr(md5($user_id . time()), 0, 8));
        
        $coupon_data = array(
            'ids' => ids(),
            'code' => $coupon_code,
            'description' => 'كوبون خصم من نقاط الولاء',
            'discount_type' => 'percentage',
            'discount_value' => $discount_percentage,
            'user_id' => $user_id,
            'usage_limit' => 1,
            'user_limit' => 1,
            'start_date' => NOW,
            'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
            'status' => 1,
            'created' => NOW
        );

        $this->db->insert('general_coupons', $coupon_data);

        // إرسال إشعار للمستخدم
        $this->send_coupon_notification($user_id, $coupon_code, $discount_percentage);
    }

    // منح خدمة مجانية
    private function grant_free_service($user_id, $service_id)
    {
        $free_service_data = array(
            'ids' => ids(),
            'user_id' => $user_id,
            'service_id' => $service_id,
            'status' => 'available',
            'expires_at' => date('Y-m-d H:i:s', strtotime('+60 days')),
            'created' => NOW
        );

        $this->db->insert('general_free_services', $free_service_data);
    }

    // إرسال إشعار الكوبون
    private function send_coupon_notification($user_id, $coupon_code, $discount)
    {
        $notification_data = array(
            'ids' => ids(),
            'user_id' => $user_id,
            'title' => 'كوبون خصم جديد!',
            'message' => "تهانينا! حصلت على كوبون خصم {$discount}% برمز: {$coupon_code}",
            'type' => 'reward',
            'is_read' => 0,
            'created' => NOW
        );

        $this->db->insert('general_notifications', $notification_data);
    }

    // عرض تاريخ النقاط
    public function point_history()
    {
        $user_id = session('uid');
        $history = $this->main_model->get_detailed_point_history($user_id);

        $data = array(
            "history" => $history
        );

        $this->template->build('point_history', $data);
    }

    // عرض مستويات الولاء
    public function levels()
    {
        $levels = $this->main_model->get_loyalty_levels();
        $user_id = session('uid');
        $user_points = $this->main_model->get_user_points($user_id);
        $current_level = $this->main_model->get_user_level($user_points);

        $data = array(
            "levels" => $levels,
            "user_points" => $user_points,
            "current_level" => $current_level
        );

        $this->template->build('levels', $data);
    }
}

// نموذج الولاء
class loyalty_model extends MY_Model {
    
    protected $tb_points = 'general_loyalty_points';
    protected $tb_rewards = 'general_loyalty_rewards';
    protected $tb_redemptions = 'general_loyalty_redemptions';
    
    // الحصول على نقاط المستخدم
    public function get_user_points($user_id)
    {
        $result = $this->db->select('SUM(points) as total_points')
                          ->where('user_id', $user_id)
                          ->get($this->tb_points)
                          ->row();
        
        return $result ? (int)$result->total_points : 0;
    }

    // إضافة نقاط للمستخدم
    public function add_points($user_id, $points, $reason, $reference_id = null)
    {
        $point_data = array(
            'ids' => ids(),
            'user_id' => $user_id,
            'points' => $points,
            'reason' => $reason,
            'reference_id' => $reference_id,
            'created' => NOW
        );

        return $this->db->insert($this->tb_points, $point_data);
    }

    // خصم نقاط من المستخدم
    public function deduct_points($user_id, $points, $reason, $reference_id = null)
    {
        return $this->add_points($user_id, -$points, $reason, $reference_id);
    }

    // الحصول على تاريخ النقاط
    public function get_point_history($user_id, $limit = 20)
    {
        return $this->db->select('*')
                       ->where('user_id', $user_id)
                       ->order_by('created', 'DESC')
                       ->limit($limit)
                       ->get($this->tb_points)
                       ->result();
    }

    // الحصول على المكافآت المتاحة
    public function get_available_rewards()
    {
        return $this->db->select('*')
                       ->where('status', 1)
                       ->order_by('points_required', 'ASC')
                       ->get($this->tb_rewards)
                       ->result();
    }

    // الحصول على مكافأة محددة
    public function get_reward($reward_id)
    {
        return $this->db->where('id', $reward_id)
                       ->where('status', 1)
                       ->get($this->tb_rewards)
                       ->row();
    }

    // استبدال مكافأة
    public function redeem_reward($user_id, $reward_id, $points_used)
    {
        $redemption_data = array(
            'ids' => ids(),
            'user_id' => $user_id,
            'reward_id' => $reward_id,
            'points_used' => $points_used,
            'status' => 'completed',
            'created' => NOW
        );

        $this->db->insert($this->tb_redemptions, $redemption_data);
        return $this->db->insert_id();
    }

    // الحصول على مستوى المستخدم
    public function get_user_level($user_points)
    {
        $levels = array(
            array('name' => 'برونزي', 'min_points' => 0, 'max_points' => 999, 'benefits' => 'خصم 5% على جميع الخدمات'),
            array('name' => 'فضي', 'min_points' => 1000, 'max_points' => 4999, 'benefits' => 'خصم 10% + دعم أولوية'),
            array('name' => 'ذهبي', 'min_points' => 5000, 'max_points' => 9999, 'benefits' => 'خصم 15% + خدمات حصرية'),
            array('name' => 'بلاتيني', 'min_points' => 10000, 'max_points' => 24999, 'benefits' => 'خصم 20% + مدير حساب مخصص'),
            array('name' => 'ماسي', 'min_points' => 25000, 'max_points' => 999999, 'benefits' => 'خصم 25% + جميع الميزات الحصرية')
        );

        foreach ($levels as $level) {
            if ($user_points >= $level['min_points'] && $user_points <= $level['max_points']) {
                return (object)$level;
            }
        }

        return (object)$levels[0]; // المستوى الافتراضي
    }

    // الحصول على جميع مستويات الولاء
    public function get_loyalty_levels()
    {
        return array(
            array('name' => 'برونزي', 'min_points' => 0, 'benefits' => 'خصم 5% على جميع الخدمات', 'color' => '#CD7F32'),
            array('name' => 'فضي', 'min_points' => 1000, 'benefits' => 'خصم 10% + دعم أولوية', 'color' => '#C0C0C0'),
            array('name' => 'ذهبي', 'min_points' => 5000, 'benefits' => 'خصم 15% + خدمات حصرية', 'color' => '#FFD700'),
            array('name' => 'بلاتيني', 'min_points' => 10000, 'benefits' => 'خصم 20% + مدير حساب مخصص', 'color' => '#E5E4E2'),
            array('name' => 'ماسي', 'min_points' => 25000, 'benefits' => 'خصم 25% + جميع الميزات الحصرية', 'color' => '#B9F2FF')
        );
    }
}
?>
