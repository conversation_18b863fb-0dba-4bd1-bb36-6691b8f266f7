<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/**
 *
 * Header
 *
 */
$lang["Statistics"]             = 'Statistics';
$lang["Services"]               = 'Services';
$lang["Order"]                  = 'Order';
$lang["order_logs"]             = "Order Logs";
$lang["New_order"]              = 'New Order';
$lang["API"]                    = 'API';
$lang["user_manager"]           = "User manager";
$lang["Subscribers"]            = "Subscribers";
$lang["user_activity_logs"]     = "User Activity Logs";
$lang["banned_ip_address_list"] = "Banned IP Address List";
$lang["system_settings"]        = "System Settings";
$lang["API_providers"]          = 'API Providers';
$lang["Language"]               = 'Language';
$lang["Documentation"]          = 'Documentation';
$lang["Support"]                = 'Support';
$lang["Profile"]                = 'Profile';
$lang["Admin_account"]          = 'Admin account';
$lang["Add_funds"]              = 'Add funds';
$lang["Add_money"]              = 'Add money';
$lang["Hi"]                     = 'Hi';
$lang["Enter_license"]          = 'Enter license';
$lang["payments"] = "Payments";
$lang["payment_bonuses"] = "Payment Bonuses";
$lang["subscribers"] = "Subscribers";

/**
 *
 * Footer
 *
 */
$lang["Quick_links"]       = "Quick Links";
$lang["terms__conditions"] = "Terms & Conditions";
$lang["Cookie_Policy"]     = "Cookie Policy";
$lang["Home"]              = "Home";
$lang["Copyright"]         = "Copyright &copy; 2019";

/**
 *
 * General
 *
 */
$lang["add_new"]          = "Add new";
$lang["Lists"]            = "Lists";
$lang["No_"]              = "No.";
$lang["Created"]          = "Created";
$lang["Updated"]          = "Updated";
$lang["Status"]           = "Status";
$lang["Action"]           = "Action";
$lang["Description"]      = "Description";
$lang["Edit"]             = "Edit";
$lang["Delete"]           = "Delete";
$lang["Active"]           = "Active";
$lang["Deactive"]         = "Deactive";
$lang["Yes"]              = "Yes";
$lang["No"]               = "No";
$lang["Email"]            = "Email";
$lang["Timezone"]         = "Time zone";
$lang["Password"]         = "Password";
$lang["Confirm_password"] = "Confirm Password";
$lang["Save"]             = "Save";
$lang["look_like_there_are_no_results_in_here"] = "Look like there are no results in here!";
$lang["Subject"] = "Subject";
$lang["Content"] = "Content";
$lang["Message"] = "Message";
$lang["Submit"]  = "Submit";
$lang["Cancel"]  = "Cancel";
$lang['Password_is_required']                         = 'Password is required';
$lang["email_is_required"]                            = "Email is required";
$lang["invalid_email_format"]                         = "Invalid email format";
$lang['Password_must_be_at_least_6_characters_long']  = 'Password must be at least 6 characters long';
$lang['Password_does_not_match_the_confirm_password'] = 'Password does not match the confirm password';
$lang['There_was_an_error_processing_your_request_Please_try_again_later'] = 'There was an error processing your request. Please try again later';
$lang["Update_successfully"]  = "Update successfully";
$lang["Deleted_successfully"] = "Deleted successfully";
$lang["the_item_does_not_exist_please_try_again"] = "The item does not exist. Please try again";
$lang['Are_you_sure_you_want_to_delete_this_item'] = 'Are you sure you want to delete this item?';
$lang['Are_you_sure_you_want_to_delete_all_items'] = 'Are you sure you want to delete all items?';
$lang["please_choose_at_least_one_item"] = "Please choose at least one item";

$lang['Search_for_'] = 'Search for...';
$lang['Sign_out']    = 'Sign out';
$lang['Sign_Up']     = 'Sign Up';
$lang['Login']       = 'Login';


$lang["note"]      = "Note:";
$lang["Facebook"]  = "Facebook";
$lang["Instagram"] = "Instagram";
$lang["Pinterest"] = "Pinterest";
$lang["Twitter"]   = "Twitter";
$lang["Paypal"]    = "Paypal";
$lang["2Checkout"] = "2Checkout";
$lang["Stripe"]    = "Stripe";


/**
 *
 * User manage
 *
 */
$lang["users"]             = "Users";
$lang["admin"]             = "Admin";
$lang["regular_user"]      = "Regular User";
$lang["Funds"]             = "Funds";
$lang["User_profile"]      = "User profile";
$lang["send_mail"]         = "Send Mail";
$lang["Edit_user"]         = "Edit user";
$lang["basic_information"] = "Basic Information";
$lang["first_name"]        = "First name";
$lang["last_name"]         = "Last name";
$lang["account_type"]      = "Account type";
$lang["note_if_you_dont_want_to_change_password_then_leave_these_password_fields_empty"] = "Note: If you don't want to change password then leave these password fields empty!";
$lang["more_informations"] = "More Informations";
$lang["whatsapp_number"]   = "WhatsApp Number";
$lang["Website"]           = "Website";
$lang["Phone"]             = "Phone";
$lang["Skype_id"]          = "Skype ID";
$lang["Address"]           = "Address";
$lang["note_if_you_dont_want_add_more_information_then_leave_these_informations_fields_empty"] = "Note: If you don't want add more information then leave these informations fields empty!";
$lang["To"] = "To";
$lang["please_fill_in_the_required_fields"] = "Please fill in the required fields";
$lang['An_account_for_the_specified_email_address_already_exists_Try_another_email_address'] = 'An account for the specified email address already exists. Try another email address';
$lang["subject_is_required"] = "Subject is required";
$lang["message_is_required"] = "Message is required";
$lang["description_is_required"] = "Description is required";
$lang["your_email_has_been_successfully_sent_to_user"] = "Your email has been successfully sent to user";
$lang["the_account_does_not_exists"] = "The account does not exists";
$lang["the_input_value_was_not_a_correct_number"] = "The input value was not a correct number";
$lang["can_not_delete_administrator_account"] = "Can not delete Administrator account";
$lang["custom_rate"] = "Custom rate(%)";
$lang["history_ip"]  = "history_ip";
$lang["view_user"]   = "View User";
$lang["Back_to_Admin"]   = "Back to Admin";
$lang["edit_custom_rates"] = "Edit custom rates";
$lang["add_custom_rate"] = "Add Custom Rate";
$lang["delete_all"] = "Delete all";
$lang["allowed_payment_methods"] = "Allowed payment methods:";
/**
 *
 * Settings
 *
 */
$lang["Settings"]           = "Settings";
$lang["general_settings"]   = "General Settings";
$lang["website_setting"]    = "WebSite Setting";
$lang["Logo"]               = "Website Logo";
$lang["terms__policy_page"] = "Terms & Policy page";
$lang["default_setting"]    = "Default Setting";
$lang["Other"]              = "Other";
$lang["email_setting"]      = "Email Setting";
$lang["email_template"]     = "Email Template";
$lang["integrations"]       = "Integrations";
$lang["Payment"]            = "Payment";

$lang["Maintenance_mode"]    = "Maintenance mode";
$lang["link_to_access_the_maintenance_mode"] = "Make sure you remmeber this link to get access Maintenance mode before you activate:";
$lang["website_name"]        = "Website name";
$lang["website_description"] = "Website description";
$lang["website_keywords"]    = "Website keywords";
$lang["website_title"]       = "Website title";
$lang["website_logo"]        = "Website Logo";
$lang["website_favicon"]     = "Website favicon";
$lang["website_logo_white"]  = "Website logo (white)";
$lang["terms__policy"]       = "Terms & Policy";
$lang["content_of_terms"]    = "Content of Terms";
$lang["content_of_policy"] = "Content of Policy";
$lang["other_settings"]     = "Other settings";
$lang["enable_https"]       = "Enable HTTPS";
$lang["emded_code"]         = "Emded Code";
$lang["social_media_links"] = "Social Media links";
$lang["header_menu_skin_colors"] = "Header Menu Skin Colors";
$lang["disable_signup_page"] = "Disable Signup Page";
$lang["note_please_make_sure_the_ssl_certificate_has_the_active_status_in_your_hosting_before__you_activate"] = "Note: Please make sure the SSL certificate has the 'Active' status in your hosting before  you activate.";
$lang["note_only_supports_javascript_code"] = "Note: Only supports Javascript code ";
$lang["contact_informations"]               = "Contact Informations";
$lang["working_hour"]                       = "Working Hour";
$lang["Tel"]                                = "Tel";
$lang["Email"]                              = "E-mail";
$lang["email_notifications"]                = "Email notifications";
$lang["new_user_welcome_email"]             = "New User Welcome Email";
$lang["new_user_notification_email"] = "New User Notification Email";
$lang["receive_notification_when_a_new_user_registers_to_the_site"] = "(Receive notification when a new user registers to the site)";
$lang["payment_notification_email"] = "Payment Notification Email";
$lang["send_notification_when_a_new_user_add_funds_successfully_to_user_balance"] = "(Send notification when a new user add funds successfully to user balance)";
$lang["ticket_notification_email"] = "Ticket Notification Email";
$lang["send_notification_to_user_when_admin_reply_to_a_ticket"] = "(Send notification to user when Admin reply to a ticket)";
$lang["send_notification_to_admin_when_user_open_a_ticket"] = "(Send notification to Admin when user open a ticket)";
$lang["order_notification_email"] = "Order Notification Email";
$lang["receive_notification_when_a_user_place_order_successfully"] = "(Receive notification when a user place order successfully)";
$lang["From"]              = "From";
$lang["your_name"]         = "Your name";
$lang["email_protocol"]    = "Email protocol";
$lang["php_mail_function"] = "PHP mail function";
$lang["recommended"]       = "(Recommended)";
$lang["sometime_email_is_going_into__recipients_spam_folders_if_php_mail_function_is_enabled"] = "Sometime, email is going into  recipients' spam folders if PHP mail function is enabled";
$lang["SMTP"]              = "SMTP";
$lang["smtp_server"]       = "SMTP Server";
$lang["smtp_port"]         = "SMTP Port";
$lang["smtp_encryption"]   = "SMTP Encryption";
$lang["smtp_username"]     = "SMTP Username";
$lang["smtp_password"]     = "SMTP Password";
$lang["password_recovery"] = "Password Recovery";
$lang["you_can_use_following_template_tags_within_the_message_template"] = "You can use following template tags within the message template:";
$lang["displays_the_users_first_name"]   = "displays the user's first name";
$lang["displays_the_users_last_name"]    = "displays the user's last name";
$lang["displays_the_users_email"]        = "displays the user's email";
$lang["displays_the_users_timezone"]     = "displays the user's timezone";
$lang["displays_recovery_password_link"] = "displays recovery password link";
$lang["payment_integration"]             = "Payment Integration";
$lang["currency_setting"]                = "Currency Setting";
$lang["currency_code"]                   = "Currency Code";
$lang["thousand_separator"] = "Thousand Separator";
$lang["decimal_separator"] = "Decimal Separator";
$lang["Dot"] = "Dot";
$lang["Comma"] = "Comma";
$lang["Space"] = "Space";
$lang["auto_currency_converter"] = "Auto Currency converter";
$lang["applying_when_you_fetch_sync_all_services_from_smm_providers"] = "(Applying when you fetch, sync all services from SMM providers)";
$lang["1_original_currency"] = "1 Original currency";
$lang["new_currency"] = "New Currency";
$lang["if_you_dont_want_to_change_currency_rate_then_leave_this_currency_rate_field_to_1"] = "If you don't want to change Currency rate then leave this currency rate field to 1";


$lang["the_paypal_payments_only_supports_these_currencies"] = "The PayPal Payments only supports these currencies:";
$lang["currency_symbol"]         = "Currency Symbol";
$lang["transaction_limits"]      = "Transaction Limits";
$lang["currency_decimal_places"] = "Currency decimal places";
$lang["minimum_amount"]          = "Minimum Amount";
$lang["Environment"]             = "Environment";
$lang["Live"]                    = "Live";
$lang["transaction_fee"]         = "Transaction fee";
$lang["sandbox_test"] 			 = "Sandbox (test)";
$lang["paypal_client_id"]        = "Paypal Client ID";
$lang["paypal_client_secret"]    = "Paypal Client Secret";
$lang["publishable_key"] = "Publishable Key";
$lang["secret_key"]      = "Secret Key";
$lang["private_key"]     = "Private Key";
$lang["2checkout_account_number_sellerid"] = "2Checkout account number (sellerId)";
$lang["auto_clear_ticket_lists"] = "Auto clear ticket lists";
$lang["default_tickets_log"] = "Default Tickets log";
$lang["clear_ticket_lists_after_x_days_without_any_response_from_user"] = "Clear Ticket lists (after X days) without any response from user";
$lang["default_service"]        = "Default Service";
$lang["default_min_order"]      = "Default Min Order";
$lang["default_max_order"]      = "Default Max Order";
$lang["default_price_per_1000"] = "Default Price per 1000";
$lang["dripfeed_option"]        = "Drip-feed option";
$lang["note_please_make_sure_the_dripfeed_feature_has_the_active_status_in_api_provider_before_you_activate"] = "Note: Please make sure the Drip-feed feature has the 'Active' status in API provider before you activate.";
$lang["default_runs"] = "Default Runs";
$lang["default_interval_in_minutes"] = "Default Interval (in minutes)";
$lang["explication_of_the_service_symbol"] = "Explication of the service symbol";
$lang["Pagination"] = "Pagination";
$lang["limit_the_maximum_number_of_rows_per_page"] = "Limit the Maximum Number of Rows per Page";
$lang["price_percentage_increase"] = "Price percentage increase";
$lang["use_for_sync_and_bulk_add_services"] = "Use for sync and Bulk add services";
$lang["displays_the_service_lists_without_login_or_register"] = "Displays the service lists without login or register";
$lang["displays_api_tab_in_header"] = "Displays API tab in header";
$lang["displays_required_skypeid_field_in_signup_page"] = "Displays required SkypeID field in signup page";
$lang["displays_google_recapcha"]    = "Displays Google reCAPTCHA";
$lang["google_recaptcha_site_key"]   = "Google reCAPTCHA site key";
$lang["google_recaptcha_serect_key"] = "Google reCAPTCHA serect key";
$lang["please_verify_recaptcha"]     = "Please verify reCAPTCHA";
$lang["email_verification_for_new_customer_accounts"] = "Email verification for new customer accounts";
$lang["email_verification_for_new_customer_accounts_preventing_spam_account"] = "Email verification for new customer accounts (Preventing Spam Account)";
$lang["default_timezone"] = "Default Timezone";
$lang["set_the_default_timezone_at_register_page"] = "Set the default timezone at Register page";
$lang["notification_popup_at_home_page"] = "Notification popup at home page";
$lang["disable_home_page_langding_page"] = "Disable Home page (Langding page)";
$lang["Default_Homepage"]                = "Default Homepage";
$lang["cookie_policy"] = "Cookie Policy";

/**
 *
 * Language
 *
 */
$lang["Language"]               = "Language";
$lang["language_code"]          = "Language code";
$lang["choose_a_language_code"] = "Choose a language code";
$lang["Default"]                = "Default";
$lang["Location"]               = "Location";
$lang["Key"]                    = "Key";
$lang["Value"]                  = "Value";
$lang["Name"]                   = "Name";
$lang["Code"]                   = "Code";
$lang["Icon"]                   = "Icon";
$lang["choose_your_country"]           = "Choose your country";
$lang["translation_editor"]            = "Translation editor";
$lang["language_code_does_not_exists"] = "Language Code does not exists";
$lang["language_code_already_exists"]  = "Language code already exists";


/**
 *
 * Transaction logs
 *
 */
$lang["Transaction_logs"]         = "Transaction logs";
$lang["User"]                     = "User";
$lang["Transaction_ID"]           = "Transaction ID";
$lang["Payment_method"]           = "Payment method";
$lang["Amount_includes_fee"]      = "Amount (includes fee)";
$lang["Amount_paid_includes_fee"] = "Amount Paid (includes fee)";
$lang["Paid"]                     = "Paid";
$lang["waiting_for_buyer_funds"]  = "Waiting for buyer funds...";
$lang["cancelled_timed_out"]      = "Cancelled/Timed Out";
/**
 *
 * Support Tickets and FAQs
 *
 */
$lang["Tickets"]                     = "Tickets";
$lang["mark_as_new"]                 = "Mark as New";
$lang["mark_as_pending"]             = "Mark as Pending";
$lang["mark_as_closed"]              = "Mark as Closed";
$lang["add_new_ticket"]              = "Add New Ticket";
$lang["Ticket_no"]                   = "Ticket #";
$lang["submit_as_closed"]            = "Submit as Closed";
$lang["submit_as_pending"]           = "Submit as Pending";
$lang["submit_as_new"]               = "Submit as New";
$lang["New"]                         = "New";
$lang["Pending"]                     = "Pending";
$lang["Closed"]                      = "Closed";
$lang["answered"]                    = "Answered";
$lang["ticket_created_successfully"] = "Ticket created successfully";
$lang["Cancellation"]                = "Cancellation";
$lang["Speed_Up"]                    = "Speed Up";
$lang["Refill"]                      = "Refill";
$lang["Unread"]                      = "Unread";
$lang["Request"]                     = "Request";
$lang["enter_the_transaction_id"] = "Enter the Transaction ID";
$lang["for_multiple_orders_please_separate_them_using_comma_example_123451234512345"] = "For multiple orders, please separate them using comma. (example: 12345,12345,12345)";
$lang["order_id_field_is_required"] = "Order ID field is required";
$lang["please_choose_a_request"] = "Please choose a request";
$lang["transaction_id_field_is_required"] = "Transaction ID field is required";
$lang["please_choose_a_payment_type"] = "Please choose a payment type";

/**
 *
 * FAQs
 *
 */
$lang["FAQs"]     = "FAQs";
$lang["Question"] = "Question";
$lang["Answer"]   = "Answer";
$lang["Default_sorting_number"]   = "Default Sorting number";
$lang["Sorting"]   = "Sort";
$lang["Edit_FAQ"] = "Edit FAQ";
$lang["question_is_required"] = "Question is required";
$lang["answer_is_required"] = "Answer is required";
$lang["sort_number_must_to_be_greater_than_zero"] = "Sort number must to be greater than zero";

/**
 *
 * API
 *
 */

$lang["api_documentation"] = "API Documentation";
$lang["note_please_read_the_api_intructions_carefully_its_your_solo_responsability_what_you_add_by_our_api"] = "Note: Please read the API intructions carefully. Its your solo responsability what you add by our API.";
$lang["response_format"] = "Response format";
$lang["http_method"] = "HTTP Method";
$lang["api_key"] = "API Key";
$lang["download_php_code_examples"] = "Download PHP Code Examples";
$lang["place_new_order"] = "Place new Order";
$lang["example_response"] = "Example response:";
$lang["status_order"] = "Status Order";
$lang["parameter"] = "Parameter";
$lang["multiple_orders_status"] = "Multiple orders status";
$lang["services_lists"] = "Services Lists";
$lang["Balance"] = "Balance";
$lang["your_api_key"] = "Your API key";
$lang["service_id"] = "Service ID";
$lang["link_to_page"] = "Link to page";
$lang["needed_quantity"] = "Needed quantity";
$lang["order_id"] = "Order ID";
$lang["order_ids_separated_by_comma_array_data"] = "Order IDs separated by comma (array data)";
$lang["api_is_disable_for_this_user_or_user_not_found_contact_the_support"] = "API is Disable for this user or User Not Found! Contact the Support";
$lang["this_action_is_invalid"] = "This action is Invalid";
$lang["there_are_missing_required_parameters_please_check_your_api_manual"] = "There are missing required parameters. Please check your API Manual";
$lang["invalid_link"] = "Invalid Link";
$lang["service_id_does_not_exists"] = "Service ID does not exists";
$lang["quantity_must_to_be_greater_than_or_equal_to_minimum_amount"] = "Total quantity must to be greater than or equal to minimum amount";
$lang["quantity_must_to_be_less_than_or_equal_to_maximum_amount"] = "Total quantity must to be less than or equal to maximum amount";
$lang["not_enough_funds_on_balance"] = "Not enough funds on balance";
$lang["order_id_is_required_parameter_please_check_your_api_manual"] = "Order ID is required parameter. Please check your API Manual";
$lang["incorrect_order_id"] = "Incorrect order ID";


/**
 *
 * Services
 *
 */
$lang["edit_service"]          = "Edit Service";
$lang["package_name"]          = "Package Name";
$lang["choose_a_category"]     = "Choose a category";
$lang["maximum_amount"]        = "Maximum Amount";
$lang["Price"]                 = "Price";
$lang["rate_per_1000"]         = "Rate per 1000";
$lang["min__max_order"]        = "Min / Max order";
$lang["name_is_required"]      = "Name is required";
$lang["category_is_required"]  = "Category is required";
$lang["min_order_is_required"] = "Min order is required";
$lang["max_order_is_required"] = "Max order is required";
$lang["max_order_must_to_be_greater_than_min_order"] = "Max order must to be greater than Min order";
$lang["price_invalid"] = "Price invalid";
$lang["currency_decimal_places_must_to_be_equal_than_2"] = "Currency decimal places must to be equal than 2";

$lang["Details"] 			= "Details";
$lang["__good_seller"] 		= "Good Seller";
$lang["__speed_level"] 		= "Speed Level";
$lang["__hot_service"] 		= "Hot service";
$lang["__best_service"] 	= "Best Service";
$lang["__drip_feed"] 		= "Drip Feed";
$lang["__cancel_button"] 	= "Cancel Button";
$lang["import_services"] = "Import Services";
$lang["actions"] = "Actions";

$lang["custom_comments"] 			     = "Custom comments";
$lang["custom_comments_package"]         = "Custom comments package";
$lang["mentions_with_hashtags"]          = "Mentions with hashtags";
$lang["mentions_custom_list"] 			 = "Mentions custom list";
$lang["mentions_hashtag"] 			     = "Mentions hashtag";
$lang["mentions_user_followers"]         = "Mentions user_followers";
$lang["mentions_media_likers"] 			 = "Mentions_media_likers";
$lang["package"] 			             = "Package";
$lang["comment_likes"] 			         = "Comment likes";
$lang["all_deactivated_services"]        = "All deactivated Services";
$lang["failed_to_delete_there_are_no_deactivate_service_now"] = "Failed to delete. There are no deactivate service now!";
/**
 *
 * Category
 *
 */
$lang["Category"]                   = "Category";
$lang["edit_category"]              = "Edit Category";
$lang["all_deactivated_categories"] = "All deactivated Categories";
$lang["failed_to_delete_there_are_no_deactivate_category_now"] = "Failed to delete. There are no deactivate Category now!";

/**
 *
 * Order
 *
 */
$lang["single_order"]                   = "Single Order";
$lang["mass_order"]                     = "Mass Order";
$lang["order_service"]                  = "Order Service";
$lang["choose_a_service"]               = "Choose a service";
$lang["Link"]                           = "Link";
$lang["Quantity"]                       = "Quantity";
$lang["yes_i_have_confirmed_the_order"] = "Yes, i have confirmed the order!";
$lang["total_charge"]                   = "Total Charge:";
$lang["order_resume"]                   = "Order Resume";
$lang["service_name"]                   = "Service name";
$lang["price_per_1000"]                 = "Price per 1000";
$lang["place_order"]                    = "Place order";
$lang["one_order_per_line_in_format"]   = "One order per line in format";
$lang["here_you_can_place_your_orders_easy_please_make_sure_you_check_all_the_prices_and_delivery_times_before_you_place_a_order_after_a_order_submited_it_cannot_be_canceled"] = "Here you can place your orders easy! Please make sure you check all the prices and delivery times before you place a order! After a order submited it cannot be canceled.";
$lang["failed"] = "Failed!";
$lang["there_was_some_issues_with_your_mass_order"] = "There was some issues with your mass order:";
$lang["order_content"] = "Order content";
$lang["error_message"] = "Error Message";
$lang["order_basic_details"] = "Order Basic Details";
$lang["sort_by"]       = "Sort by";
$lang["All"]           = "All";
$lang["Completed"]     = "Completed";
$lang["Awaiting"]      = "Awaiting";
$lang["Processing"]    = "Processing";
$lang["In_progress"]   = "In progress";
$lang["Partial"]       = "Partial";
$lang["Error"]         = "Error";
$lang["Canceled"]      = "Canceled";
$lang["Refunded"]      = "Refunded";
$lang["Edit_Order"]    = "Edit Order";
$lang["Start_counter"] = "Start counter";
$lang["Remains"]       = "Remains";
$lang["Amount"]        = "Amount";
$lang["Charge"]        = "Charge";
$lang["Service"]       = "Service";
$lang["service_does_not_exists"]                    = "Service does not exists";
$lang["order_amount_exceeds_available_funds"]       = "Order amount exceeds available funds!";
$lang["order_amount_exceeds_available_the_min_max"] = "Order amount exceeds available minimum or maximum!";
$lang["please_choose_a_category"]                   = "Please choose a category";
$lang["please_choose_a_service"]                    = "Please choose a service";
$lang["category_does_not_exists"]                   = "Category does not exists";
$lang["quantity_is_required"]                       = "Quantity is required";
$lang["quantity_must_to_be_greater_than_or_equal_to_minimum_amount"] = "Quantity must to be greater than or equal to minimum amount";
$lang["quantity_must_to_be_less_than_or_equal_to_maximum_amount"] = "Quantity must to be less than or equal to maximum amount";
$lang["you_must_confirm_to_the_conditions_before_place_order"] = "You must confirm to the conditions before place order";
$lang["place_order_successfully"]                    = "Place Order successfully";
$lang["field_cannot_be_blank"]                       = "Field cannot be blank";
$lang["you_do_not_have_enough_funds_to_place_order"] = "You do not have enough funds to Place order";
$lang["invalid_format_place_order"]                  = "Invalid format place order";
$lang["link_is_required"]                            = "Link is required";
$lang["start_counter_is_a_number_format"]            = "Start counter is a number format";
$lang["remains_is_a_number_format"]                  = "Remains is a number format";
$lang["dripfeed"]                                    = "Drip-feed ";
$lang["what_is_dripfeed"]                            = "What is Drip-feed?";
$lang["Runs"]                                        = "Runs";
$lang["interval_in_minutes"]                         = "Interval (in minutes)";
$lang["interval"]                                    = "Interval";
$lang["total_quantity"]                              = "Total Quantity";
$lang["runs_is_required"]                            = "Runs is required";
$lang["interval_time_is_required"] 					 = "Interval time is required";
$lang["interval_time_must_to_be_less_than_or_equal_to_60_minutes"] = "Interval time must to be less than or equal to 60 minutes";

$lang["drip_feed_desc"] = "<p><strong>Drip-Feed</strong> is a service that we are offering so you would be able to put the same order multiple times automatically.</p>
                        <p>Example: let's say you want to get 1000 likes on your Instagram Post but you want to get 100 likes each 30 minutes, you will put:</p>
                        <ul>
                          <li>Link: Your Post Link</li>
                          <li>Quantity: 100 </li>
                          <li>Runs: 10</li>
                          <li>Interval: 30</li>
                        </ul>
                        <p>
                          <strong>Note:</strong> Never order more quantity than the maximum which is written on the service name (Quantity x Runs), Example if the service's max is 4000, you don’t put Quantity: 500 and Run: 10, because total quantity will be 500x10 = 5000 which is bigger than the service max (4000). Also never put the Interval below the actual start time (some services need 60 minutes to start, don’t put Interval less than the service start time or it will cause a fail in your order).
                        </p>";



$lang["Comments"]  = "Comments";
$lang["Usernames"] = "Usernames";
$lang["Hashtag"]   = "Hashtag";
$lang["Media_Url"] = "Media Url";
$lang["hashtags_format_hashtag"]       = "Hashtags (Format: #hashtag)";
$lang["hashtag_field_is_required"]     = "Hashtag field is required";
$lang["username_field_is_required"]    = "Username field is required";
$lang["comments_field_is_required"]    = "Comments field is required";
$lang["min_cannot_be_higher_than_max"] = "Min cannot be higher than Max";
$lang["incorrect_delay"] = "Incorrect delay";
$lang["min"] = "min";
$lang["max"] = "max";
$lang["minimum_1_post"] = "minimum 1 post";
$lang["new_posts_future_posts_must_to_be_greater_than_or__equal_to_1"] = "New Posts (Future posts) must to be greater than or  equal to 1";
$lang["1_per_line"] = "(1 per line)";

/**
 *
 * Subscriptions
 *
 */
$lang["Subscriptions"] = "Subscriptions";
$lang["No_delay"]      = "No delay";
$lang["minutes"]       = "minutes";
$lang["Posts"]         = "Posts";
$lang["New_posts"]     = "New posts";
$lang["Actived_Posts"] = "Actived Posts";
$lang["Username"]      = "Username";
$lang["Expiry"]        = "Expiry";
$lang["Delay"]         = "Delay";
$lang["Active"]        = "Active";
$lang["Paused"]        = "Paused";
$lang["Expired"]       = "Expired";


/**
 *
 * Statistics
 *
 */

$lang["total_users"] = "Total Users";
$lang["your_balance"] = "Your Balance";
$lang["total_orders"] = "Total Orders";
$lang["total_tickets"] = "Total Tickets";
$lang["total_transactions"] = "Total Transactions";
$lang["recent_orders"] = "Recent Orders";
$lang["recent_tickets"] = "Recent Tickets";
$lang["total_amount_recieved"] = "Total Amount Recieved";
$lang["total_amount_spent"] = "Total Amount Spent";
$lang["total_users_balance"] = "Total Users' Balance";
$lang["total_providers_balance"] = "Total Providers' balance";
$lang["total_profit_30_days"] = "Total Profit 30 days";
$lang["total_profit_today"] = "Total Profit Today";
$lang["last_5_orders"] = "Last 5 Orders";
$lang["top_newest_users"] = "Top newest Users";
$lang["last_5_newest_users"] = "Last 5 Newest Users";
$lang["top_bestsellers"] = "Top bestsellers";



/**
 *
 * Profile
 *
 */
$lang["Your_account"] = "Your account";
$lang["Generate_new"] = "Generate new";


/**
 *
 * Add Funds
 *
 */
$lang["manual_payment"]  = "Manual Payment";
$lang["Minimal_payment"] = "Minimal Payment";
$lang["you_can_deposit_funds_with_paypal_they_will_be_automaticly_added_into_your_account"] = "You can deposit funds with %s® they will be automaticly added into your account!";
$lang["amount_usd"] = "Amount (%s)";
$lang["yes_i_understand_after_the_funds_added_i_will_not_ask_fraudulent_dispute_or_chargeback"] = "Yes, I understand after the funds added i will not ask fraudulent dispute or charge-back!";
$lang["this_payment_gateway_is_not_already_active_at_the_present"] = "This Payment Gateway is not already active at the present!";
$lang["Pay"] = "Pay";
$lang["you_can_make_a_manual_payment_to_cover_an_outstanding_balance_you_can_use_any_payment_method_in_your_billing_account_for_manual_once_done_open_a_ticket_and_contact_with_administrator"] = "You can make a manual payment to cover an outstanding balance. Once time, open a ticket and contact with Administrator.";
$lang["amount_is_required"]               = "Amount is required";
$lang["amount_must_be_greater_than_zero"] = "Amount must be greater than zero";
$lang["minimum_amount_is"]                = "Minimum Amount is";
$lang["you_must_confirm_to_the_conditions_before_paying"] = "You must confirm to the conditions before paying";
$lang["processing_"]         = "Processing ....!";
$lang["payment_sucessfully"] = "Payment sucessfully!";
$lang["your_payment_has_been_processed_here_are_the_details_of_this_transaction_for_your_reference"] = "Your payment has been processed. Here are the details of this transaction for your reference:";
$lang["payment_unsucessfully"] = "Payment unsucessfully!";

$lang["sorry_your_payment_failed_no_charges_were_made"] = "Sorry, your payment failed. No charges were made";
$lang["2checkout_creditdebit_card_payment"] = "2Checkout Credit/Debit card Payment";
$lang["stripe_creditdebit_card_payment"]    = "Stripe Credit/Debit card Payment";
$lang["user_information"]                   = "User information";
$lang["card_number"]                        = "CARD NUMBER";
$lang["expiry_date"]                        = "EXPIRY DATE";
$lang["there_is_no_any_payment_gateway_at_the_present"] = "There is no any payment gateway at the present!";
$lang["payment_gateway"] = "Payment Gateway";
$lang["empty"] = "Empty";
$lang["transaction_id_was_sent_to_your_email"] = "(Transaction ID was sent to your email)";
$lang["total_amount_XX_includes_fee"] = "Total Amount (%s) (Includes fee):";
$lang["currency_rate"] = "Currency Rate";
$lang["please_do_not_refresh_this_page"] = "Please do not refresh this page...";
$lang["Deposit_to_"] = "Deposit_to_";
$lang["clicking_return_to_shop_merchant_after_payment_successfully_completed"] = "Clicking <strong class='text-danger'>Return to Shop (Merchant)</strong> after payment successfully completed";

$lang["user_can_send_a_maximum_of_each_payment_worth"] = "User can send a maximum of each payment worth ";
$lang["paypal_transaction_fees_applies"] = "PayPal Transaction Fees applies";
$lang["more_details"] = "(More details)";
$lang["this_payment_is_for_services_and_it_is_not_refundable"] = "THIS PAYMENT IS FOR SERVICES AND IT IS NOT REFUNDABLE";
$lang["to_send_more_than__all_users_need_to_open_a_new_ticket"] = 'To Send More, All Users need to open a new ticket.';

/**
 *
 * Landing page
 *
 */
 
$lang["resellers_1_destination_for_smm_services"] = "Resellers' #1 Destination for SMM Services";
$lang["save_time_managing_your_social_account_in_one_panel_where_people_buy_smm_services_such_as_facebook_ads_management_instagram_youtube_twitter_soundcloud_website_ads_and_many_more"] = "Save time managing your social account in one panel. Where people buy SMM services such as Facebook ads management, Instagram, YouTube, Twitter, Soundcloud, Website ads and many more!";
$lang["get_start_now"] = "Get start now!";
$lang["best_smm_marketing_services"] = "Best SMM Marketing Services!";
$lang["best_smm_marketing_services_desc"] = "We provide the cheapest SMM Reseller Panel services amongst our competitors. If you’re looking for a super-easy way to offer additional marketing services to your existing and new clients, look no further! our site offers that and more ! <br><br>You can resell our services in any site or Link your site through API and start resell our services directly start building stronger relationships, and helping you make a great profit at the same time. We do the work so you can focus on what you do best! As you grow, your profit grows without having to hire more people. This allows you to expand your business without all the expense and headaches usually associated with growing bigger!";
$lang["What_we_offer"] = "What we offer!";
$lang["you_can_resell_our_services_and_grow_your_profit_easily_resellers_are_important_part_of_smm_panel"] = "You can resell our services and grow your profit easily, Resellers are important part of SMM PANEL";
$lang["technical_support_for_all_our_services_247_to_help_you"] = "Technical support for all our services 24/7 to help you";
$lang["get_the_best_high_quality_services_and_in_less_time_here"] = "Get the best high quality services and in less time here";
$lang["services_are_updated_daily_in_order_to_be_further_improved_and_to_provide_you_with_best_experience"] = "Services are updated daily In order to be further improved and to provide you with best experience";
$lang["we_have_api_support_for_panel_owners_so_you_can_resell_our_services_easily"] = "We have API Support For panel owners so you can resell our services easily";
$lang["we_have_a_popular_methods_as_paypal_and_many_more_can_be_enabled_upon_request"] = "We have a Popular methods as PayPal and many more can be enabled upon request";
$lang["Resellers"] = "Resellers";
$lang["secure_payments"] = "Secure Payments";
$lang["Supports"] = "Supports";
$lang["Updates"] = "Updates";
$lang["api_support"] = "Api support";
$lang["high_quality_services"] = "High quality services";
$lang["ready_to_start_with_us"] = "READY TO START WITH US?";
$lang["Terms__Privacy_Policy"] = "Terms & Privacy Policy";
$lang["Terms"] = "Terms";
$lang["Privacy_Policy"] = "Privacy Policy";
$lang["Notification"] = "Notification!";
$lang["Close"] = "Close";
$lang["register_and_try_for_free_we_give_you_1_to_get_started"] = "Register and try for FREE. We give you € 1 to get started!";


/**
 *
 * Login
 *
 */
$lang["login_to_your_account"] = "Login to your account";
$lang["only_letters_and_white_space_allowed"] = "Only letters and white space allowed";
$lang["remember_me"]           = "Remember me";
$lang["forgot_password"]       = "Forgot password";
$lang["dont_have_account_yet"] = "Don't have account yet?";

$lang["enter_your_registration_email_address_to_receive_password_reset_instructions"] = "Enter your registration email address to receive password reset instructions.";
$lang["new_password"]         = "New Password";
$lang["register_now"]         = "Register Now";
$lang["create_new_account"]   = "Create new account";
$lang["i_agree_the"]          = "I agree the";
$lang["already_have_account"] = "Already have account?";
$lang["oops_you_must_agree_with_the_terms_of_services_or_privacy_policy"] = "Oops! You must agree with the Terms of Services or Privacy Policy";
$lang["welcome_you_have_signed_up_successfully"] = "Welcome! you have signed up successfully.";
$lang["your_account_has_not_been_activated"] = "Your account has not been activated";
$lang["Login_successfully"] = "Login successfully";
$lang["email_address_and_password_that_you_entered_doesnt_match_any_account_please_check_your_account_again"] = "Email address and password that You entered doesn't match any account. Please check your account again";
$lang["we_have_send_you_a_link_to_reset_password_and_get_back_into_your_account_please_check_your_email"] = "We have send you a link to reset password and get back into your account. Please check your email";
$lang["your_password_has_been_successfully_changed"] = "Your password has been successfully changed";
$lang["thank_you_for_signing_up_please_check_your_email_to_complete_the_account_verification_process"] = "Thank you for signing up! Please check your email to complete the Account Verification Process";
$lang["congratulations_your_registration_is_now_complete"] = "Congratulations! Your Registration is Now Complete";
$lang["congratulations_desc"] = "Welcome to our service! We're happy to have you as a part of our community. Your account has been successfully created. You can access your account by clicking on the button below.";


/*----------  API providers  ----------*/
$lang["api_providers_list"] = "API Providers List";
$lang["update_api"] 		= "Update API";
$lang["update_balance"]     = "Update Balance";
$lang["Type"] 				= "Type";
$lang["Manual"] 			= "Manual";
$lang["API"] 				= "API";
$lang["edit_api"]                     = "Edit API";
$lang["api_key"]                      = "API Key";
$lang["api_url"]                      = "API Url";
$lang["list_of_api_services"]         = "List of API Services";
$lang["choose_a_api_provider"]        = "Choose a API Provider";
$lang["add_service"]                  = "Add service";
$lang["services_list_via_api"]        = "Services list via API";
$lang["api_provider_does_not_exists"] = "API Provider does not exists.";
$lang["api_url_is_required"]          = "API URL is required";
$lang["api_key_is_required"]          = "API KEY is required";
$lang["sorry_the_service_id_already_exists"] = "Sorry! The Service ID already exists";
$lang["add_new_service_via_api"] = "Add New Service via API";
$lang["api_orderid"]             = "API OrderID";
$lang["API_Response"]             = "API Response";
$lang["bulk_add_all_services"]   = "Bulk Add All Services";
$lang["api_provider_name"]       = "API Provider Name";
$lang["api_provider"]            = "API Provider";
$lang["api_service_id"]            = "API ServiceID";
$lang["price_percentage_increase_auto_rounding_to_2_decimal_places"] = "Price percentage increase (Auto rounding to 2 decimal places)";
$lang["bulk_add_limit"] = "Bulk add limit";
$lang["note_when_you_use_this_feature_the_system_will_bulk_add_services_categories_from_api_provider_and_set_price_percentage_increase"] = "Note: When you use this feature, the system will bulk add services, categories from API provider and set price percentage increase";
$lang["price_percentage_increase_in_invalid_format"] = "Price Percentage increase in invalid format";
$lang["bulk_add_limit_in_invalid_format"] = "Bulk add limit in invalid format";

$lang["add_edit_provider_note_desc"] = "Note: This script supports most of all API Providers (API v2) like: momopanel.com, vinasmm.com, hqsmartpanel.com etc. So it doesn't support another API provider which have different API Parameters";

$lang["sync_services"] = "Sync Services";
$lang["Disabled"]      = "Disabled";
$lang["synchronization_results"] = "Synchronization results";
$lang["synchronous_request"] = "Synchronous request";
$lang["current_service"] = "Current Services";
$lang["current_service_sync_all_the_current_services"] = "Current Service: Sync all the current services";
$lang["all_auto_add_new_service_if_the_service_doesnt_exists"] = "All: Auto add new service if the service doesn't exists";
$lang["add_update_service"] = "Add/Update service";
$lang["service_lists_are_empty_unable_to_sync_services"] = "Service lists are empty. Unable to sync services!";
$lang["there_seems_to_be_an_issue_connecting_to_api_provider_please_check_api_key_and_token_again"] = "There seems to be an issue connecting to API provider. Please check API key and Token again!";
$lang["price_invalid_format"] = "Price invalid format";
$lang["auto_rounding_to_X_decimal_places"] = "(Auto rounding to %s decimal places)";
$lang["sync_min_max_dripfeed"] = "Sync Min, Max, DripFeed";
$lang["sync_new_price"] = "Sync New Price";
$lang["sync_original_price"] = "Sync Original Price";

$lang["auto_convert_to_new_currency_with_currency_rate_like_in"] = "Auto convert to new currency with currency rate like in ";
$lang["currency_setting_page"] = "Currency Setting page";
$lang["auto_sync_services_setting"] = "Auto Sync Services Setting";
$lang["sync_service_name"] = "Sync Service Name";
$lang["sync_services_description_only_support_hq_smartpanel"] = "Sync Services Description (Only Support HQ SmartPanel)";


/**
 *
 * Maintenance mode
 *
 */
$lang["login_to_maintenace_mode"] = "Login to Maintenace Mode";
$lang["use_admin_account"] = "(Use Admin account)";
$lang["the_website_is_in_maintenance_mode"] = "The website is in maintenance mode";
$lang["were_undergoing_a_bit_of_scheduled_maintenance_sorry_for_the_inconvenience_well_be_backup_and_running_as_fast_as_possible"] = "We're undergoing a bit of scheduled maintenance. Sorry for the inconvenience. We'll be backup and running as fast as possible!";


/**
 *
 * News & annoucement
 *
 */

$lang["displays_news__announcement_feature"] = "Displays News & Announcement feature";
$lang["news__announcement"] = "News & Announcement";
$lang["New_services"] = "New services";
$lang["Updated_services"] = "Updated service";
$lang["Announcement"] = "Announcement";
$lang["Disabled_services"] = "Disabled services";
$lang["View"] = "View";
$lang["edit_news_announcement"] = "Edit News/Announcement";
$lang["Start"] = "Start";
$lang["Expired"] = "Expired";
$lang["whats_new_on_smartpanel"] = "What's new on SmartPanel";
$lang["invalid_news_type"] = "Invalid news type!";
$lang["start_field_is_required"] = "Start field is required";
$lang["Description_field_is_required"] = "Description field is required";
$lang["expiry_field_is_required"] = "Expiry field is required";

/**
 *
 * Module
 *
 */
$lang["Modules"] = "Modules";
$lang["Purchased"] = "Purchased";
$lang["Buy_now"] = "Buy Now";
$lang["Upgrade_version"] = "Upgrade to version ";
$lang["Expired"] = "Expiry field is required";


/**
 *
 * User Activity Logs
 *
 */
$lang["Clear_all"]  = "Clear all";
$lang["Role"]       = "Role";
$lang["Action"]     = "Action";
$lang["IP_Address"] = "IP_Address";
$lang["Location"]   = "Location";
$lang["Date_Time"]  = "DateTime";
$lang["Check_in"]   = "Check in";
$lang["Check_out"]  = "Check out";


/**
 *
 * Banned IP Address list
 *
 */

$lang["Banned_By"]     = "Banned By";
$lang["newsletter"] = "Newsletter";
$lang["fill_in_the_ridiculously_small_form_below_to_receive_our_ridiculously_cool_newsletter"] = "Fill in the ridiculously small form below to receive our ridiculously cool newsletter!";
$lang["subscribe_now"] = "Subscribe now";
$lang["you_subscribed_successfully_to_our_newsletter_thank_you_for_your_subsrciption"] = "You subscribed successfully to our newsletter. Thank you for your subsrciption";
$lang["an_error_occurred_while_subscribing_please_try_again"] = "An error occurred while subscribing. Please try again.";
$lang["a_subscriber_for_the_specified_email_address_already_exists_try_another_email_address"] = "A subscriber for the specified email address already exists. Try another email address";
$lang["cookie_policy_page"] = "Cookie Policy Page";


/*----------  Payments bonues  ----------*/
$lang["payments_bonuses"] = "Payments Bonuses";
$lang["method"] = "Method";
$lang["bonus_percentage"] = "Bonus Percentage (%)";
$lang["bonus_from"] = "Bonus From";
$lang["add_bonus"] = "Add bonus";
$lang["no_payment_option"] = "No Payment Option";


/*----------  Payments Lists  ----------*/
$lang["payments_methods"] = "Payments Methods";
$lang["new_users"] = "New users";
$lang["method_name"] = "Method name";
$lang["Minimal_payment"] = "Minimal payment";
$lang["Maximal_payment"] = "Maximal payment";
$lang["allowed"] = "Allowed";
$lang["not_allowed"] = "Not Allowed";
$lang["take_fee_from_user"] = "Take fee from user";
$lang["Knet"] = "Knet";
$lang["Credit_card"] = "Credit card";

