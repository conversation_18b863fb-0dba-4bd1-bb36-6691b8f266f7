<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * تشغيل هذا الملف للتحقق من صحة الاتصال
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'irjnpfzw_hcr';
$db_pass = 'irjnpfzw_hcr';
$db_name = 'irjnpfzw_hcr';

echo "<h2>🔧 اختبار الاتصال بقاعدة البيانات</h2>";
echo "<hr>";

// اختبار الاتصال
try {
    $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ <strong>نجح الاتصال بالخادم!</strong><br>";
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
    if ($stmt->rowCount() > 0) {
        echo "✅ <strong>قاعدة البيانات موجودة!</strong><br>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "✅ <strong>نجح الاتصال بقاعدة البيانات!</strong><br><br>";
        
        // التحقق من الجداول المطلوبة
        $required_tables = [
            'general_users',
            'general_sessions', 
            'general_options',
            'general_lang_list',
            'general_services',
            'general_orders',
            'general_categories',
            'general_purchase'
        ];
        
        echo "<h3>📋 التحقق من الجداول:</h3>";
        
        foreach ($required_tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ الجدول <strong>$table</strong> موجود<br>";
            } else {
                echo "❌ الجدول <strong>$table</strong> غير موجود<br>";
            }
        }
        
        echo "<br>";
        
        // التحقق من المستخدم الإداري
        $stmt = $pdo->query("SELECT COUNT(*) FROM general_users WHERE role = 'admin'");
        $admin_count = $stmt->fetchColumn();
        
        if ($admin_count > 0) {
            echo "✅ <strong>يوجد مستخدم إداري في النظام</strong><br>";
            
            $stmt = $pdo->query("SELECT email FROM general_users WHERE role = 'admin' LIMIT 1");
            $admin_email = $stmt->fetchColumn();
            echo "📧 البريد الإلكتروني للمدير: <strong>$admin_email</strong><br>";
            echo "🔑 كلمة المرور الافتراضية: <strong>123456</strong><br>";
        } else {
            echo "❌ <strong>لا يوجد مستخدم إداري</strong><br>";
        }
        
        echo "<br>";
        
        // التحقق من اللغة العربية
        $stmt = $pdo->query("SELECT COUNT(*) FROM general_lang_list WHERE code = 'ar'");
        $ar_count = $stmt->fetchColumn();
        
        if ($ar_count > 0) {
            echo "✅ <strong>اللغة العربية مضافة للنظام</strong><br>";
        } else {
            echo "❌ <strong>اللغة العربية غير مضافة</strong><br>";
        }
        
        // التحقق من إعدادات النظام
        $stmt = $pdo->query("SELECT COUNT(*) FROM general_options WHERE name = 'verification_bypass'");
        $bypass_count = $stmt->fetchColumn();
        
        if ($bypass_count > 0) {
            echo "✅ <strong>تم تجاوز التحقق من مفتاح الشراء</strong><br>";
        } else {
            echo "❌ <strong>لم يتم تجاوز التحقق من مفتاح الشراء</strong><br>";
        }
        
    } else {
        echo "❌ <strong>قاعدة البيانات غير موجودة!</strong><br>";
        echo "💡 <strong>الحل:</strong> تشغيل ملف fix_database_connection.sql<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ <strong>خطأ في الاتصال:</strong> " . $e->getMessage() . "<br>";
    
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "<br>💡 <strong>الحلول المقترحة:</strong><br>";
        echo "1. تحقق من صحة اسم المستخدم وكلمة المرور<br>";
        echo "2. تأكد من أن المستخدم له صلاحيات الوصول لقاعدة البيانات<br>";
        echo "3. تحقق من إعدادات cPanel<br>";
    }
    
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "<br>💡 <strong>الحل:</strong> إنشاء قاعدة البيانات من cPanel<br>";
    }
}

echo "<br><hr>";
echo "<h3>📝 معلومات الاتصال الحالية:</h3>";
echo "🖥️ <strong>الخادم:</strong> $db_host<br>";
echo "👤 <strong>المستخدم:</strong> $db_user<br>";
echo "🗄️ <strong>قاعدة البيانات:</strong> $db_name<br>";

echo "<br><hr>";
echo "<h3>🔧 خطوات الإصلاح:</h3>";
echo "1. تشغيل ملف <strong>fix_database_connection.sql</strong> في phpMyAdmin<br>";
echo "2. التأكد من صحة إعدادات قاعدة البيانات في cPanel<br>";
echo "3. إعادة تشغيل هذا الملف للتحقق من الإصلاح<br>";
echo "4. حذف هذا الملف بعد التأكد من عمل النظام<br>";

echo "<br><hr>";
echo "<p><small>تم إنشاء هذا الملف بواسطة Augment Agent</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007cba;
    padding-bottom: 5px;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

strong {
    color: #007cba;
}

.success {
    color: #28a745;
}

.error {
    color: #dc3545;
}

.info {
    color: #17a2b8;
}
</style>
